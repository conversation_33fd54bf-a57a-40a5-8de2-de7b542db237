"""浏览器控制模块"""

import time
from typing import Dict, Any, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class BrowserControl:
    """浏览器控制类"""
    
    def __init__(self):
        """初始化浏览器控制"""
        self.driver: Optional[webdriver.Chrome] = None
        self.default_browser = config.get('actions.browser.default_browser', 'chrome')
        self.headless = config.get('actions.browser.headless', False)
        self.timeout = config.get('actions.browser.timeout', 30)
    
    def _ensure_driver(self):
        """确保浏览器驱动已初始化"""
        if self.driver is None:
            self._init_driver()
    
    def _init_driver(self):
        """初始化浏览器驱动"""
        try:
            if self.default_browser == 'chrome':
                options = webdriver.ChromeOptions()
                if self.headless:
                    options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                self.driver = webdriver.Chrome(options=options)
            elif self.default_browser == 'firefox':
                options = webdriver.FirefoxOptions()
                if self.headless:
                    options.add_argument('--headless')
                self.driver = webdriver.Firefox(options=options)
            elif self.default_browser == 'edge':
                options = webdriver.EdgeOptions()
                if self.headless:
                    options.add_argument('--headless')
                self.driver = webdriver.Edge(options=options)
            else:
                raise ValueError(f"不支持的浏览器: {self.default_browser}")
            
            logger.info(f"初始化浏览器: {self.default_browser}")
        except Exception as e:
            logger.error(f"初始化浏览器失败: {e}")
            raise
    
    def open_url(self, url: str) -> Dict[str, Any]:
        """
        打开网页
        
        Args:
            url: 网页 URL
            
        Returns:
            执行结果
        """
        try:
            self._ensure_driver()
            
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            self.driver.get(url)
            time.sleep(1)  # 等待页面加载
            
            logger.info(f"打开网页: {url}")
            return {
                "success": True,
                "url": url,
                "title": self.driver.title
            }
        except Exception as e:
            logger.error(f"打开网页失败: {e}")
            return {"success": False, "error": str(e)}
    
    def click_element(self, selector: str, by: str = 'css') -> Dict[str, Any]:
        """
        点击元素
        
        Args:
            selector: 元素选择器
            by: 选择器类型 ('css', 'xpath', 'id', 'name', 'class', 'tag')
            
        Returns:
            执行结果
        """
        try:
            self._ensure_driver()
            
            by_map = {
                'css': By.CSS_SELECTOR,
                'xpath': By.XPATH,
                'id': By.ID,
                'name': By.NAME,
                'class': By.CLASS_NAME,
                'tag': By.TAG_NAME
            }
            
            by_type = by_map.get(by, By.CSS_SELECTOR)
            
            wait = WebDriverWait(self.driver, self.timeout)
            element = wait.until(EC.element_to_be_clickable((by_type, selector)))
            element.click()
            
            logger.info(f"点击元素: {selector}")
            return {"success": True, "selector": selector}
        except TimeoutException:
            logger.error(f"元素未找到或不可点击: {selector}")
            return {"success": False, "error": "元素未找到或不可点击"}
        except Exception as e:
            logger.error(f"点击元素失败: {e}")
            return {"success": False, "error": str(e)}
    
    def fill_input(self, selector: str, text: str, by: str = 'css') -> Dict[str, Any]:
        """
        填写输入框
        
        Args:
            selector: 元素选择器
            text: 要输入的文本
            by: 选择器类型
            
        Returns:
            执行结果
        """
        try:
            self._ensure_driver()
            
            by_map = {
                'css': By.CSS_SELECTOR,
                'xpath': By.XPATH,
                'id': By.ID,
                'name': By.NAME,
                'class': By.CLASS_NAME,
                'tag': By.TAG_NAME
            }
            
            by_type = by_map.get(by, By.CSS_SELECTOR)
            
            wait = WebDriverWait(self.driver, self.timeout)
            element = wait.until(EC.presence_of_element_located((by_type, selector)))
            element.clear()
            element.send_keys(text)
            
            logger.info(f"填写输入框: {selector}")
            return {"success": True, "selector": selector, "text": text}
        except Exception as e:
            logger.error(f"填写输入框失败: {e}")
            return {"success": False, "error": str(e)}
    
    def extract_text(self, selector: str, by: str = 'css') -> Dict[str, Any]:
        """
        提取文本
        
        Args:
            selector: 元素选择器
            by: 选择器类型
            
        Returns:
            执行结果
        """
        try:
            self._ensure_driver()
            
            by_map = {
                'css': By.CSS_SELECTOR,
                'xpath': By.XPATH,
                'id': By.ID,
                'name': By.NAME,
                'class': By.CLASS_NAME,
                'tag': By.TAG_NAME
            }
            
            by_type = by_map.get(by, By.CSS_SELECTOR)
            
            wait = WebDriverWait(self.driver, self.timeout)
            element = wait.until(EC.presence_of_element_located((by_type, selector)))
            text = element.text
            
            logger.info(f"提取文本: {selector}")
            return {"success": True, "selector": selector, "text": text}
        except Exception as e:
            logger.error(f"提取文本失败: {e}")
            return {"success": False, "error": str(e)}
    
    def scroll(self, direction: str = 'down', amount: int = 300) -> Dict[str, Any]:
        """
        滚动页面
        
        Args:
            direction: 滚动方向 ('up', 'down')
            amount: 滚动量（像素）
            
        Returns:
            执行结果
        """
        try:
            self._ensure_driver()
            
            if direction == 'down':
                self.driver.execute_script(f"window.scrollBy(0, {amount});")
            else:
                self.driver.execute_script(f"window.scrollBy(0, -{amount});")
            
            logger.info(f"滚动页面: {direction} {amount}px")
            return {"success": True, "direction": direction, "amount": amount}
        except Exception as e:
            logger.error(f"滚动页面失败: {e}")
            return {"success": False, "error": str(e)}
    
    def screenshot(self, filename: str) -> Dict[str, Any]:
        """
        截取网页截图
        
        Args:
            filename: 保存文件名
            
        Returns:
            执行结果
        """
        try:
            self._ensure_driver()
            
            self.driver.save_screenshot(filename)
            
            logger.info(f"截取网页截图: {filename}")
            return {"success": True, "filename": filename}
        except Exception as e:
            logger.error(f"截取截图失败: {e}")
            return {"success": False, "error": str(e)}
    
    def close(self) -> Dict[str, Any]:
        """
        关闭浏览器
        
        Returns:
            执行结果
        """
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                logger.info("关闭浏览器")
            return {"success": True}
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
            return {"success": False, "error": str(e)}
    
    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

