# Playwright MCP 启动脚本（PowerShell）
# 用法：在 PowerShell 中运行：
#   .\playwright_mcp_launch.ps1
# 或者带参数：
#   .\playwright_mcp_launch.ps1 -Port 8931 -Isolated -SaveTrace

param(
    [int]
    $Port = 8931,
    [switch]
    $Isolated,
    [switch]
    $SaveTrace,
    [string]
    $OutputDir = "./mcp-output",
    [string]
    $Host = "localhost"
)

Write-Host "Starting Playwright MCP server on $Host:$Port"

$argsList = @()

if ($Isolated) { $argsList += "--isolated" }
if ($SaveTrace) { $argsList += "--save-trace" }
$argsList += "--port"; $argsList += "$Port"
$argsList += "--host"; $argsList += "$Host"

# Ensure output dir exists
if (!(Test-Path -Path $OutputDir)) { New-Item -ItemType Directory -Path $OutputDir | Out-Null }
$argsList += "--output-dir"; $argsList += $OutputDir

Write-Host "Running: npx @playwright/mcp@latest $($argsList -join ' ')"

# Note: Start-Process would detach; use & to run in current terminal so logs are visible
& npx @playwright/mcp@latest @argsList
