# WinTest - Windows Use Service Dependencies

# Windows-Use - Main package for Windows automation
windows-use>=0.6.1

# Core LLM dependencies
openai>=1.0.0
anthropic>=0.18.0
langchain-google-genai>=1.0.0
pydantic>=2.0.0
pyyaml>=6.0

# Computer control for Windows
pyautogui>=0.9.54
pyperclip>=1.8.2
psutil>=5.9.0
pillow>=10.0.0

# Windows-specific automation
pywin32>=306
pygetwindow>=0.0.9
pycaw>=20220416

# Browser automation
selenium>=4.15.0
playwright>=1.40.0

# Screen and OCR
pytesseract>=0.3.10
opencv-python>=4.8.0

# NLP and utilities
python-dotenv>=1.0.0
colorama>=0.4.6
rich>=13.0.0

# Database support
cx_Oracle>=8.3.0
oracledb>=1.4.0

# Excel support
openpyxl>=3.1.0
pandas>=2.0.0
xlsxwriter>=3.1.0

# Windows service support
pywin32-ctypes>=0.2.2
wmi>=1.5.1

# Optional: Local LLM support
# transformers>=4.35.0
# torch>=2.1.0
