"""Oracle Forms 操作测试"""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock

from src.actions.oracle_forms import OracleFormsOperations
from src.actions.excel_ops import ExcelOperations


class TestExcelOperations(unittest.TestCase):
    """Excel 操作测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.excel_ops = ExcelOperations()
        self.test_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.test_dir, "test.xlsx")
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_write_to_excel(self):
        """测试写入 Excel"""
        data = [
            {'name': 'Alice', 'age': 30, 'city': 'Beijing'},
            {'name': 'Bob', 'age': 25, 'city': 'Shanghai'},
            {'name': '<PERSON>', 'age': 35, 'city': 'Guangzhou'}
        ]
        
        result = self.excel_ops.write_to_excel(
            data=data,
            filename=self.test_file,
            sheet_name='Test'
        )
        
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(self.test_file))
        self.assertEqual(result['rows'], 3)
        self.assertEqual(result['columns'], 3)
    
    def test_write_to_excel_styled(self):
        """测试写入带样式的 Excel"""
        data = [
            {'product': 'Laptop', 'price': 5000, 'quantity': 10},
            {'product': 'Mouse', 'price': 50, 'quantity': 100}
        ]
        
        result = self.excel_ops.write_to_excel_styled(
            data=data,
            filename=self.test_file,
            sheet_name='Products',
            title='Product List'
        )
        
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(self.test_file))
    
    def test_read_from_excel(self):
        """测试读取 Excel"""
        # 先写入数据
        data = [
            {'id': 1, 'name': 'Test1'},
            {'id': 2, 'name': 'Test2'}
        ]
        
        self.excel_ops.write_to_excel(data, self.test_file)
        
        # 读取数据
        result = self.excel_ops.read_from_excel(self.test_file)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['rows'], 2)
        self.assertEqual(len(result['data']), 2)
    
    def test_append_to_excel(self):
        """测试追加数据到 Excel"""
        # 初始数据
        initial_data = [
            {'id': 1, 'name': 'First'}
        ]
        
        self.excel_ops.write_to_excel(initial_data, self.test_file)
        
        # 追加数据
        new_data = [
            {'id': 2, 'name': 'Second'},
            {'id': 3, 'name': 'Third'}
        ]
        
        result = self.excel_ops.append_to_excel(new_data, self.test_file)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['rows'], 3)
    
    def test_write_empty_data(self):
        """测试写入空数据"""
        result = self.excel_ops.write_to_excel([], self.test_file)
        
        self.assertFalse(result['success'])
        self.assertIn('error', result)


class TestOracleFormsOperations(unittest.TestCase):
    """Oracle Forms 操作测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.oracle_forms = OracleFormsOperations()
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
        self.oracle_forms.cleanup()
    
    @patch('src.actions.oracle_forms.DatabaseOperations')
    def test_extract_from_database(self, mock_db):
        """测试从数据库提取数据"""
        # Mock 数据库操作
        mock_db_instance = mock_db.return_value
        mock_db_instance.connect_oracle.return_value = {'success': True}
        mock_db_instance.execute_query.return_value = {
            'success': True,
            'data': [
                {'id': 1, 'name': 'Test1'},
                {'id': 2, 'name': 'Test2'}
            ],
            'row_count': 2
        }
        
        # 执行提取
        result = self.oracle_forms.extract_form_data_from_database(
            db_config={
                'username': 'test',
                'password': 'test',
                'host': 'localhost',
                'port': 1521,
                'service_name': 'TEST'
            },
            query='SELECT * FROM test_table'
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['row_count'], 2)
    
    @patch('src.actions.oracle_forms.InputControl')
    def test_extract_by_tab(self, mock_input):
        """测试通过 Tab 键提取数据"""
        # Mock 输入控制
        mock_input_instance = mock_input.return_value
        mock_input_instance.clipboard_get.return_value = {
            'success': True,
            'text': 'Test Value'
        }
        
        # 执行提取
        result = self.oracle_forms.extract_form_fields_by_tab(
            field_names=['field1', 'field2'],
            start_position=(100, 100)
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['field_count'], 2)
    
    def test_export_to_excel(self):
        """测试导出到 Excel"""
        data = [
            {'employee_id': 1, 'name': 'Alice', 'department': 'IT'},
            {'employee_id': 2, 'name': 'Bob', 'department': 'HR'}
        ]
        
        test_file = os.path.join(self.test_dir, 'export_test.xlsx')
        
        result = self.oracle_forms.export_to_excel(
            data=data,
            filename=test_file,
            styled=True,
            title='Employee Data'
        )
        
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(test_file))
        self.assertEqual(result['rows'], 2)
    
    @patch('src.actions.oracle_forms.DatabaseOperations')
    def test_extract_and_export(self, mock_db):
        """测试提取并导出（集成测试）"""
        # Mock 数据库操作
        mock_db_instance = mock_db.return_value
        mock_db_instance.connect_oracle.return_value = {'success': True}
        mock_db_instance.execute_query.return_value = {
            'success': True,
            'data': [
                {'id': 1, 'value': 'Test1'},
                {'id': 2, 'value': 'Test2'}
            ],
            'row_count': 2
        }
        
        test_file = os.path.join(self.test_dir, 'integrated_test.xlsx')
        
        # 执行提取并导出
        result = self.oracle_forms.extract_and_export(
            extraction_method='database',
            extraction_config={
                'db_config': {
                    'username': 'test',
                    'password': 'test',
                    'host': 'localhost',
                    'port': 1521,
                    'service_name': 'TEST'
                },
                'query': 'SELECT * FROM test_table'
            },
            excel_filename=test_file,
            excel_config={
                'sheet_name': 'Test Data',
                'styled': True
            }
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['records_extracted'], 2)
        self.assertTrue(os.path.exists(test_file))


if __name__ == '__main__':
    unittest.main()

