"""浏览器自动化示例"""

from src.core.agent import ComputerUseAgent
import time


def main():
    """浏览器自动化示例"""
    agent = ComputerUseAgent()
    
    print("=" * 60)
    print("浏览器自动化示例")
    print("=" * 60)
    
    # 打开网页
    print("\n1. 打开 Google")
    result = agent.execute("打开 google.com")
    print(f"   结果: {'成功' if result.get('success') else '失败'}")
    
    time.sleep(2)
    
    # 截取网页截图
    print("\n2. 截取网页截图")
    result = agent.execute("截取当前屏幕并保存为 google.png")
    print(f"   结果: {'成功' if result.get('success') else '失败'}")
    
    # 清理
    agent.cleanup()
    
    print("\n" + "=" * 60)
    print("示例执行完成")
    print("=" * 60)


if __name__ == "__main__":
    main()

