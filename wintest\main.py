#!/usr/bin/env python3
"""
WinTest - Windows Use Service Main Entry Point

基于 Windows-Use 框架的 Windows 自动化服务
支持多种 LLM 提供商：OpenAI、Google Gemini、OpenRouter 等
"""

import os
import sys
import argparse
from typing import Optional
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown

# 加载环境变量
load_dotenv()

console = Console()

def print_banner():
    """打印欢迎横幅"""
    banner = """
    ╔═══════════════════════════════════════════════════════╗
    ║                                                       ║
    ║              WinTest v1.0.0                           ║
    ║                                                       ║
    ║          基于 Windows-Use 的自动化服务                  ║
    ║                                                       ║
    ╚═══════════════════════════════════════════════════════╝
    """
    console.print(banner, style="bold cyan")

def print_help():
    """打印帮助信息"""
    help_text = """
## 支持的 LLM 提供商

### OpenDataSky (推荐)
- 设置环境变量: `OPENDATASKY_API_KEY`, `OPENDATASKY_BASE_URL`
- 支持模型: claude-sonnet-4-20250514, gpt-3.5-turbo, gpt-4 等
- 基础 URL: http://server.opendatasky.com/v1/api/open-ai/ds

### OpenAI
- 设置环境变量: `OPENAI_API_KEY`
- 支持模型: gpt-4, gpt-4-turbo, gpt-3.5-turbo

### Google Gemini
- 设置环境变量: `GOOGLE_API_KEY`
- 支持模型: gemini-2.5-flash, gemini-pro

### OpenRouter
- 设置环境变量: `OPENROUTER_API_KEY`
- 支持多种模型: openai/gpt-4o, anthropic/claude-3-sonnet

## 使用示例

### 基本命令
- "打开记事本并输入 Hello World"
- "截取当前屏幕"
- "打开浏览器访问 google.com"
- "创建一个名为 test.txt 的文件"

### 特殊命令
- `help` - 显示帮助
- `exit` 或 `quit` - 退出程序
- `clear` - 清屏
    """
    console.print(Panel(Markdown(help_text), title="帮助", border_style="green"))

def create_openai_agent():
    """创建 OpenAI 代理"""
    try:
        from langchain_openai import ChatOpenAI
        from windows_use.agent import Agent

        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            console.print("❌ 请设置 OPENAI_API_KEY 环境变量", style="red")
            return None

        llm = ChatOpenAI(model="gpt-4", temperature=0.2, api_key=api_key)
        agent = Agent(llm=llm, browser='edge', use_vision=False)
        console.print("✅ OpenAI 代理创建成功", style="green")
        return agent
    except Exception as e:
        console.print(f"❌ 创建 OpenAI 代理失败: {e}", style="red")
        return None

def create_gemini_agent():
    """创建 Google Gemini 代理"""
    try:
        from langchain_google_genai.chat_models import ChatGoogleGenerativeAI
        from windows_use.agent import Agent

        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            console.print("❌ 请设置 GOOGLE_API_KEY 环境变量", style="red")
            return None

        llm = ChatGoogleGenerativeAI(model='gemini-2.5-flash', temperature=0.2)
        agent = Agent(llm=llm, browser='edge', use_vision=False)
        console.print("✅ Google Gemini 代理创建成功", style="green")
        return agent
    except Exception as e:
        console.print(f"❌ 创建 Google Gemini 代理失败: {e}", style="red")
        return None

def create_openrouter_agent():
    """创建 OpenRouter 代理"""
    try:
        # 使用 OpenAI 兼容的方式连接 OpenRouter
        from langchain_openai import ChatOpenAI
        from windows_use.agent import Agent

        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            console.print("❌ 请设置 OPENROUTER_API_KEY 环境变量", style="red")
            return None

        # OpenRouter 使用 OpenAI 兼容的 API
        llm = ChatOpenAI(
            model="openai/gpt-4o",
            temperature=0.2,
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1"
        )
        agent = Agent(llm=llm, browser='edge', use_vision=False)
        console.print("✅ OpenRouter 代理创建成功", style="green")
        return agent
    except Exception as e:
        console.print(f"❌ 创建 OpenRouter 代理失败: {e}", style="red")
        return None

def create_opendatasky_agent():
    """创建 OpenDataSky 代理"""
    try:
        from langchain_openai import ChatOpenAI
        from windows_use.agent import Agent

        api_key = os.getenv("OPENDATASKY_API_KEY")
        base_url = os.getenv("OPENDATASKY_BASE_URL")

        if not api_key:
            console.print("❌ 请设置 OPENDATASKY_API_KEY 环境变量", style="red")
            return None

        if not base_url:
            console.print("❌ 请设置 OPENDATASKY_BASE_URL 环境变量", style="red")
            return None

        # OpenDataSky 使用 OpenAI 兼容的 API
        llm = ChatOpenAI(
            model="claude-sonnet-4-20250514",  # 使用 Claude Sonnet 4 模型
            temperature=0.2,
            api_key=api_key,
            base_url=base_url
        )
        agent = Agent(llm=llm, browser='edge', use_vision=False)
        console.print("✅ OpenDataSky 代理创建成功", style="green")
        return agent
    except Exception as e:
        console.print(f"❌ 创建 OpenDataSky 代理失败: {e}", style="red")
        return None

def auto_create_agent():
    """自动创建可用的代理"""
    console.print("🔍 正在检测可用的 LLM 提供商...", style="yellow")
    
    # 按优先级尝试创建代理
    providers = [
        ("OpenDataSky", create_opendatasky_agent),
        ("OpenRouter", create_openrouter_agent),
        ("OpenAI", create_openai_agent),
        ("Google Gemini", create_gemini_agent),
    ]
    
    for provider_name, create_func in providers:
        console.print(f"尝试创建 {provider_name} 代理...", style="blue")
        agent = create_func()
        if agent:
            return agent
    
    console.print("❌ 无法创建任何代理，请检查 API 密钥配置", style="red")
    return None

def interactive_mode(provider: Optional[str] = None):
    """交互模式"""
    print_banner()
    console.print("\n输入 'help' 查看帮助，输入 'exit' 退出\n", style="yellow")
    
    # 创建代理
    if provider == "opendatasky":
        agent = create_opendatasky_agent()
    elif provider == "openai":
        agent = create_openai_agent()
    elif provider == "gemini":
        agent = create_gemini_agent()
    elif provider == "openrouter":
        agent = create_openrouter_agent()
    else:
        agent = auto_create_agent()
    
    if not agent:
        console.print("❌ 无法创建代理，程序退出", style="red")
        return
    
    console.print("\n🤖 代理已准备就绪，开始对话...\n", style="green")
    
    try:
        while True:
            try:
                # 获取用户输入
                query = console.input("\n[bold blue]您的指令[/bold blue]: ")
                
                # 处理特殊命令
                if query.lower() in ['exit', 'quit', '退出']:
                    console.print("👋 再见！", style="yellow")
                    break
                elif query.lower() in ['help', '帮助']:
                    print_help()
                    continue
                elif query.lower() in ['clear', '清屏']:
                    os.system('cls' if os.name == 'nt' else 'clear')
                    print_banner()
                    continue
                elif not query.strip():
                    continue
                
                # 执行指令
                console.print(f"\n🔄 正在执行: {query}", style="blue")
                try:
                    response = agent.invoke(query)
                    console.print(f"✅ 执行完成: {response}", style="green")
                except Exception as exec_error:
                    console.print(f"❌ 执行失败: {exec_error}", style="red")
                
            except KeyboardInterrupt:
                console.print("\n\n👋 程序被用户中断", style="yellow")
                break
            except Exception as e:
                console.print(f"\n❌ 执行出错: {e}", style="red")
                
    except Exception as e:
        console.print(f"❌ 程序异常: {e}", style="red")

def command_mode(command: str, provider: Optional[str] = None):
    """命令模式"""
    console.print(f"🔄 执行命令: {command}", style="blue")
    
    # 创建代理
    if provider == "opendatasky":
        agent = create_opendatasky_agent()
    elif provider == "openai":
        agent = create_openai_agent()
    elif provider == "gemini":
        agent = create_gemini_agent()
    elif provider == "openrouter":
        agent = create_openrouter_agent()
    else:
        agent = auto_create_agent()
    
    if not agent:
        console.print("❌ 无法创建代理", style="red")
        return
    
    try:
        response = agent.invoke(command)
        console.print(f"✅ 执行完成: {response}", style="green")
    except Exception as e:
        console.print(f"❌ 执行失败: {e}", style="red")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="WinTest - 基于 Windows-Use 的自动化服务"
    )
    parser.add_argument(
        '-c', '--command',
        type=str,
        help='直接执行指令（非交互模式）'
    )
    parser.add_argument(
        '-p', '--provider',
        type=str,
        choices=['opendatasky', 'openai', 'gemini', 'openrouter'],
        help='指定 LLM 提供商'
    )
    
    args = parser.parse_args()
    
    # 选择模式
    if args.command:
        command_mode(args.command, args.provider)
    else:
        interactive_mode(args.provider)

if __name__ == "__main__":
    main()
