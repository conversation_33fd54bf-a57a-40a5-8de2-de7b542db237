"""屏幕操作模块"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

import pyautogui
from PIL import Image

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class ScreenOperations:
    """屏幕操作类"""
    
    def __init__(self):
        """初始化屏幕操作"""
        self.screenshot_format = config.get('actions.screen.screenshot_format', 'png')
        self.screenshot_dir = config.get('actions.screen.screenshot_dir', 'screenshots')
        
        # 创建截图目录
        Path(self.screenshot_dir).mkdir(parents=True, exist_ok=True)
    
    def get_screen_size(self) -> Dict[str, Any]:
        """
        获取屏幕尺寸
        
        Returns:
            执行结果
        """
        try:
            size = pyautogui.size()
            logger.info(f"屏幕尺寸: {size.width}x{size.height}")
            return {
                "success": True,
                "width": size.width,
                "height": size.height
            }
        except Exception as e:
            logger.error(f"获取屏幕尺寸失败: {e}")
            return {"success": False, "error": str(e)}
    
    def screenshot(
        self,
        filename: Optional[str] = None,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> Dict[str, Any]:
        """
        截取屏幕
        
        Args:
            filename: 保存文件名（None 表示自动生成）
            region: 截图区域 (left, top, width, height)
            
        Returns:
            执行结果
        """
        try:
            # 生成文件名
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.{self.screenshot_format}"
            
            # 确保文件在截图目录中
            if not os.path.isabs(filename):
                filename = os.path.join(self.screenshot_dir, filename)
            
            # 截图
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            # 保存
            screenshot.save(filename)
            
            logger.info(f"截取屏幕: {filename}")
            return {
                "success": True,
                "filename": filename,
                "region": region
            }
        except Exception as e:
            logger.error(f"截取屏幕失败: {e}")
            return {"success": False, "error": str(e)}
    
    def find_on_screen(
        self,
        image_path: str,
        confidence: float = 0.8
    ) -> Dict[str, Any]:
        """
        在屏幕上查找图像
        
        Args:
            image_path: 要查找的图像路径
            confidence: 匹配置信度 (0-1)
            
        Returns:
            执行结果
        """
        try:
            location = pyautogui.locateOnScreen(
                image_path,
                confidence=confidence
            )
            
            if location:
                center = pyautogui.center(location)
                logger.info(f"找到图像: {image_path} at ({center.x}, {center.y})")
                return {
                    "success": True,
                    "found": True,
                    "x": center.x,
                    "y": center.y,
                    "left": location.left,
                    "top": location.top,
                    "width": location.width,
                    "height": location.height
                }
            else:
                logger.info(f"未找到图像: {image_path}")
                return {
                    "success": True,
                    "found": False
                }
        except Exception as e:
            logger.error(f"查找图像失败: {e}")
            return {"success": False, "error": str(e)}
    
    def find_all_on_screen(
        self,
        image_path: str,
        confidence: float = 0.8
    ) -> Dict[str, Any]:
        """
        在屏幕上查找所有匹配的图像
        
        Args:
            image_path: 要查找的图像路径
            confidence: 匹配置信度 (0-1)
            
        Returns:
            执行结果
        """
        try:
            locations = list(pyautogui.locateAllOnScreen(
                image_path,
                confidence=confidence
            ))
            
            results = []
            for location in locations:
                center = pyautogui.center(location)
                results.append({
                    "x": center.x,
                    "y": center.y,
                    "left": location.left,
                    "top": location.top,
                    "width": location.width,
                    "height": location.height
                })
            
            logger.info(f"找到 {len(results)} 个匹配: {image_path}")
            return {
                "success": True,
                "count": len(results),
                "locations": results
            }
        except Exception as e:
            logger.error(f"查找图像失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_pixel_color(self, x: int, y: int) -> Dict[str, Any]:
        """
        获取指定位置的像素颜色
        
        Args:
            x: X 坐标
            y: Y 坐标
            
        Returns:
            执行结果
        """
        try:
            color = pyautogui.pixel(x, y)
            logger.info(f"获取像素颜色: ({x}, {y}) = {color}")
            return {
                "success": True,
                "x": x,
                "y": y,
                "r": color[0],
                "g": color[1],
                "b": color[2],
                "hex": "#{:02x}{:02x}{:02x}".format(color[0], color[1], color[2])
            }
        except Exception as e:
            logger.error(f"获取像素颜色失败: {e}")
            return {"success": False, "error": str(e)}
    
    def ocr_screenshot(
        self,
        region: Optional[Tuple[int, int, int, int]] = None,
        lang: str = 'eng'
    ) -> Dict[str, Any]:
        """
        对屏幕截图进行 OCR 文字识别
        
        Args:
            region: 识别区域 (left, top, width, height)
            lang: 语言代码 ('eng', 'chi_sim' 等)
            
        Returns:
            执行结果
        """
        try:
            import pytesseract
            
            # 截图
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            # OCR 识别
            text = pytesseract.image_to_string(screenshot, lang=lang)
            
            logger.info(f"OCR 识别完成，提取 {len(text)} 个字符")
            return {
                "success": True,
                "text": text,
                "region": region
            }
        except ImportError:
            logger.error("pytesseract 未安装")
            return {"success": False, "error": "pytesseract 未安装"}
        except Exception as e:
            logger.error(f"OCR 识别失败: {e}")
            return {"success": False, "error": str(e)}

