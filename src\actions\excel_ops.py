"""Excel 操作模块"""

from typing import Dict, Any, List, Optional
from pathlib import Path
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class ExcelOperations:
    """Excel 操作类"""
    
    def __init__(self):
        """初始化 Excel 操作"""
        self.max_file_size = config.get('security.max_file_size_mb', 100) * 1024 * 1024
    
    def write_to_excel(
        self,
        data: List[Dict[str, Any]],
        filename: str,
        sheet_name: str = "Sheet1",
        include_header: bool = True,
        auto_width: bool = True
    ) -> Dict[str, Any]:
        """
        将数据写入 Excel 文件
        
        Args:
            data: 数据列表，每个元素是一个字典
            filename: 文件名
            sheet_name: 工作表名称
            include_header: 是否包含表头
            auto_width: 是否自动调整列宽
            
        Returns:
            执行结果
        """
        try:
            if not data:
                return {"success": False, "error": "数据为空"}
            
            # 转换为 DataFrame
            df = pd.DataFrame(data)
            
            # 创建 Excel 写入器
            file_path = Path(filename)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(
                    writer,
                    sheet_name=sheet_name,
                    index=False,
                    header=include_header
                )
                
                # 获取工作表
                worksheet = writer.sheets[sheet_name]
                
                # 自动调整列宽
                if auto_width:
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"成功写入 Excel 文件: {filename}, {len(data)} 行数据")
            return {
                "success": True,
                "filename": str(file_path.resolve()),
                "rows": len(data),
                "columns": len(df.columns)
            }
        except Exception as e:
            logger.error(f"写入 Excel 失败: {e}")
            return {"success": False, "error": str(e)}
    
    def write_to_excel_styled(
        self,
        data: List[Dict[str, Any]],
        filename: str,
        sheet_name: str = "Sheet1",
        title: str = None
    ) -> Dict[str, Any]:
        """
        将数据写入 Excel 文件（带样式）
        
        Args:
            data: 数据列表
            filename: 文件名
            sheet_name: 工作表名称
            title: 标题
            
        Returns:
            执行结果
        """
        try:
            if not data:
                return {"success": False, "error": "数据为空"}
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # 样式定义
            header_font = Font(bold=True, color="FFFFFF", size=12)
            header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 写入标题
            current_row = 1
            if title:
                ws.merge_cells(f'A1:{chr(64 + len(data[0]))}1')
                title_cell = ws['A1']
                title_cell.value = title
                title_cell.font = Font(bold=True, size=14)
                title_cell.alignment = Alignment(horizontal="center", vertical="center")
                current_row = 2
            
            # 写入表头
            columns = list(data[0].keys())
            for col_idx, column in enumerate(columns, 1):
                cell = ws.cell(row=current_row, column=col_idx)
                cell.value = column
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border
            
            # 写入数据
            for row_idx, row_data in enumerate(data, current_row + 1):
                for col_idx, column in enumerate(columns, 1):
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.value = row_data.get(column)
                    cell.border = border
                    cell.alignment = Alignment(vertical="center")
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            file_path = Path(filename)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            wb.save(filename)
            
            logger.info(f"成功写入带样式的 Excel 文件: {filename}")
            return {
                "success": True,
                "filename": str(file_path.resolve()),
                "rows": len(data),
                "columns": len(columns)
            }
        except Exception as e:
            logger.error(f"写入 Excel 失败: {e}")
            return {"success": False, "error": str(e)}
    
    def read_from_excel(
        self,
        filename: str,
        sheet_name: str = None,
        header_row: int = 0
    ) -> Dict[str, Any]:
        """
        从 Excel 文件读取数据
        
        Args:
            filename: 文件名
            sheet_name: 工作表名称（None 表示第一个工作表）
            header_row: 表头行号（0-based）
            
        Returns:
            执行结果
        """
        try:
            file_path = Path(filename)
            
            if not file_path.exists():
                return {"success": False, "error": "文件不存在"}
            
            if file_path.stat().st_size > self.max_file_size:
                return {"success": False, "error": "文件过大"}
            
            # 读取 Excel
            if sheet_name:
                df = pd.read_excel(filename, sheet_name=sheet_name, header=header_row)
            else:
                df = pd.read_excel(filename, header=header_row)
            
            # 转换为字典列表
            data = df.to_dict('records')
            
            logger.info(f"成功读取 Excel 文件: {filename}, {len(data)} 行数据")
            return {
                "success": True,
                "filename": str(file_path.resolve()),
                "data": data,
                "rows": len(data),
                "columns": len(df.columns)
            }
        except Exception as e:
            logger.error(f"读取 Excel 失败: {e}")
            return {"success": False, "error": str(e)}
    
    def append_to_excel(
        self,
        data: List[Dict[str, Any]],
        filename: str,
        sheet_name: str = "Sheet1"
    ) -> Dict[str, Any]:
        """
        追加数据到 Excel 文件
        
        Args:
            data: 数据列表
            filename: 文件名
            sheet_name: 工作表名称
            
        Returns:
            执行结果
        """
        try:
            if not data:
                return {"success": False, "error": "数据为空"}
            
            file_path = Path(filename)
            
            # 如果文件存在，读取现有数据
            if file_path.exists():
                existing_result = self.read_from_excel(filename, sheet_name)
                if existing_result['success']:
                    existing_data = existing_result['data']
                    combined_data = existing_data + data
                else:
                    combined_data = data
            else:
                combined_data = data
            
            # 写入合并后的数据
            result = self.write_to_excel(combined_data, filename, sheet_name)
            
            if result['success']:
                logger.info(f"成功追加 {len(data)} 行数据到 {filename}")
            
            return result
        except Exception as e:
            logger.error(f"追加数据到 Excel 失败: {e}")
            return {"success": False, "error": str(e)}

