"""应用程序控制模块"""

import os
import sys
import subprocess
import psutil
from typing import Dict, Any, List, Optional

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class AppControl:
    """应用程序控制类"""
    
    def __init__(self):
        """初始化应用控制"""
        self.blocked_apps = config.get('security.blocked_apps', [])
        self.timeout = config.get('actions.app_control.timeout', 10)
    
    def _is_blocked(self, app_name: str) -> bool:
        """检查应用是否被阻止"""
        app_lower = app_name.lower()
        for blocked in self.blocked_apps:
            if blocked.lower() in app_lower or app_lower in blocked.lower():
                return True
        return False
    
    def open_app(self, app_name: str, args: List[str] = None) -> Dict[str, Any]:
        """
        打开应用程序
        
        Args:
            app_name: 应用程序名称或路径
            args: 命令行参数
            
        Returns:
            执行结果
        """
        try:
            if self._is_blocked(app_name):
                return {"success": False, "error": f"应用 {app_name} 被阻止"}
            
            # 常见应用的映射
            app_map = {
                'notepad': 'notepad.exe',
                '记事本': 'notepad.exe',
                'calculator': 'calc.exe',
                '计算器': 'calc.exe',
                'chrome': 'chrome.exe',
                'firefox': 'firefox.exe',
                'edge': 'msedge.exe',
                'explorer': 'explorer.exe',
                '资源管理器': 'explorer.exe',
            }
            
            # 转换应用名称
            app_to_open = app_map.get(app_name.lower(), app_name)
            
            # 构建命令
            cmd = [app_to_open]
            if args:
                cmd.extend(args)
            
            # 启动应用
            if sys.platform == 'win32':
                process = subprocess.Popen(
                    cmd,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            else:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            
            logger.info(f"打开应用: {app_name} (PID: {process.pid})")
            return {
                "success": True,
                "app_name": app_name,
                "pid": process.pid
            }
        except Exception as e:
            logger.error(f"打开应用失败: {e}")
            return {"success": False, "error": str(e)}
    
    def close_app(self, app_name: str = None, pid: int = None) -> Dict[str, Any]:
        """
        关闭应用程序
        
        Args:
            app_name: 应用程序名称
            pid: 进程ID
            
        Returns:
            执行结果
        """
        try:
            closed_count = 0
            
            if pid:
                # 通过 PID 关闭
                try:
                    process = psutil.Process(pid)
                    process.terminate()
                    process.wait(timeout=self.timeout)
                    closed_count = 1
                    logger.info(f"关闭进程: PID {pid}")
                except psutil.NoSuchProcess:
                    return {"success": False, "error": f"进程 {pid} 不存在"}
            elif app_name:
                # 通过名称关闭
                if self._is_blocked(app_name):
                    return {"success": False, "error": f"应用 {app_name} 被阻止"}
                
                for proc in psutil.process_iter(['name', 'pid']):
                    try:
                        if app_name.lower() in proc.info['name'].lower():
                            proc.terminate()
                            proc.wait(timeout=self.timeout)
                            closed_count += 1
                            logger.info(f"关闭进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            else:
                return {"success": False, "error": "必须提供 app_name 或 pid"}
            
            if closed_count > 0:
                return {
                    "success": True,
                    "closed_count": closed_count
                }
            else:
                return {"success": False, "error": "未找到匹配的进程"}
        except Exception as e:
            logger.error(f"关闭应用失败: {e}")
            return {"success": False, "error": str(e)}
    
    def list_running_apps(self) -> Dict[str, Any]:
        """
        列出正在运行的应用
        
        Returns:
            执行结果
        """
        try:
            apps = []
            for proc in psutil.process_iter(['name', 'pid', 'status']):
                try:
                    apps.append({
                        'name': proc.info['name'],
                        'pid': proc.info['pid'],
                        'status': proc.info['status']
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            logger.info(f"列出运行中的应用: {len(apps)} 个")
            return {"success": True, "apps": apps, "count": len(apps)}
        except Exception as e:
            logger.error(f"列出应用失败: {e}")
            return {"success": False, "error": str(e)}
    
    def switch_to_app(self, app_name: str) -> Dict[str, Any]:
        """
        切换到指定应用
        
        Args:
            app_name: 应用程序名称
            
        Returns:
            执行结果
        """
        try:
            # Windows 平台使用 pyautogui
            import pyautogui
            
            # 使用 Alt+Tab 切换（简化实现）
            # 实际应用中可能需要更复杂的窗口管理
            pyautogui.hotkey('alt', 'tab')
            
            logger.info(f"切换到应用: {app_name}")
            return {"success": True, "app_name": app_name}
        except Exception as e:
            logger.error(f"切换应用失败: {e}")
            return {"success": False, "error": str(e)}

