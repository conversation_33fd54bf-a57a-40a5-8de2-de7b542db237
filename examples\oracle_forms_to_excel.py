"""Oracle Forms 数据提取到 Excel 示例"""

from src.actions.oracle_forms import OracleFormsOperations


def example_1_extract_by_database():
    """
    示例 1: 从数据库直接查询数据并导出到 Excel
    
    这是最推荐的方法，因为它直接从数据库获取数据，准确且高效。
    """
    print("=" * 60)
    print("示例 1: 从数据库提取数据并导出到 Excel")
    print("=" * 60)
    
    oracle_forms = OracleFormsOperations()
    
    # 数据库配置
    db_config = {
        'username': 'your_username',
        'password': 'your_password',
        'host': 'localhost',
        'port': 1521,
        'service_name': 'ORCL'  # 或使用 'sid': 'ORCL'
    }
    
    # SQL 查询 - 根据你的表单对应的表结构修改
    query = """
        SELECT 
            employee_id,
            employee_name,
            department,
            position,
            salary,
            hire_date
        FROM employees
        WHERE department = 'IT'
        ORDER BY employee_id
    """
    
    # 提取并导出
    result = oracle_forms.extract_and_export(
        extraction_method='database',
        extraction_config={
            'db_config': db_config,
            'query': query
        },
        excel_filename='employees_data.xlsx',
        excel_config={
            'sheet_name': '员工数据',
            'styled': True,
            'title': 'IT部门员工信息'
        }
    )
    
    if result['success']:
        print(f"✓ 成功导出 {result['records_extracted']} 条记录")
        print(f"  文件位置: {result['excel_file']}")
    else:
        print(f"✗ 导出失败: {result['error']}")
    
    oracle_forms.cleanup()


def example_2_extract_by_tab():
    """
    示例 2: 通过 Tab 键导航提取表单字段
    
    适用于需要从 Oracle Forms 界面直接提取当前显示的数据。
    """
    print("\n" + "=" * 60)
    print("示例 2: 通过 Tab 键导航提取表单字段")
    print("=" * 60)
    
    oracle_forms = OracleFormsOperations()
    
    # 定义字段名称（按 Tab 顺序）
    field_names = [
        '员工编号',
        '员工姓名',
        '部门',
        '职位',
        '工资',
        '入职日期'
    ]
    
    # 提取并导出
    # 注意：运行前需要手动打开 Oracle Forms 并定位到第一个字段
    print("\n请确保：")
    print("1. Oracle Forms 应用已打开")
    print("2. 光标已定位到第一个字段")
    print("3. 按回车键继续...")
    input()
    
    result = oracle_forms.extract_and_export(
        extraction_method='tab',
        extraction_config={
            'field_names': field_names,
            'start_position': None,  # 如果需要，可以指定起始位置 (x, y)
            'tab_count': len(field_names)
        },
        excel_filename='form_data_by_tab.xlsx',
        excel_config={
            'sheet_name': '表单数据',
            'styled': True,
            'title': 'Oracle Forms 数据提取'
        }
    )
    
    if result['success']:
        print(f"✓ 成功导出数据")
        print(f"  文件位置: {result['excel_file']}")
    else:
        print(f"✗ 导出失败: {result['error']}")
    
    oracle_forms.cleanup()


def example_3_extract_by_ocr():
    """
    示例 3: 通过 OCR 识别提取表单字段
    
    适用于无法通过其他方式访问数据的情况。
    """
    print("\n" + "=" * 60)
    print("示例 3: 通过 OCR 识别提取表单字段")
    print("=" * 60)
    
    oracle_forms = OracleFormsOperations()
    
    # 定义字段区域（需要根据实际表单位置调整）
    # 区域格式: (left, top, width, height)
    field_regions = [
        {'name': '员工编号', 'region': (100, 150, 200, 30)},
        {'name': '员工姓名', 'region': (100, 200, 200, 30)},
        {'name': '部门', 'region': (100, 250, 200, 30)},
        {'name': '职位', 'region': (100, 300, 200, 30)},
        {'name': '工资', 'region': (100, 350, 200, 30)},
        {'name': '入职日期', 'region': (100, 400, 200, 30)}
    ]
    
    print("\n请确保：")
    print("1. Oracle Forms 应用已打开并显示数据")
    print("2. 表单位置与配置的区域匹配")
    print("3. 按回车键继续...")
    input()
    
    result = oracle_forms.extract_and_export(
        extraction_method='ocr',
        extraction_config={
            'field_regions': field_regions,
            'lang': 'chi_sim'  # 中文识别，英文用 'eng'
        },
        excel_filename='form_data_by_ocr.xlsx',
        excel_config={
            'sheet_name': '表单数据',
            'styled': True,
            'title': 'Oracle Forms OCR 数据提取'
        }
    )
    
    if result['success']:
        print(f"✓ 成功导出数据")
        print(f"  文件位置: {result['excel_file']}")
    else:
        print(f"✗ 导出失败: {result['error']}")
    
    oracle_forms.cleanup()


def example_4_batch_extract():
    """
    示例 4: 批量提取多条记录
    
    从数据库批量提取数据。
    """
    print("\n" + "=" * 60)
    print("示例 4: 批量提取多条记录")
    print("=" * 60)
    
    oracle_forms = OracleFormsOperations()
    
    # 数据库配置
    db_config = {
        'username': 'your_username',
        'password': 'your_password',
        'host': 'localhost',
        'port': 1521,
        'service_name': 'ORCL'
    }
    
    # 批量查询
    query = """
        SELECT 
            order_id,
            customer_name,
            product_name,
            quantity,
            unit_price,
            total_amount,
            order_date,
            status
        FROM orders
        WHERE order_date >= TO_DATE('2024-01-01', 'YYYY-MM-DD')
        ORDER BY order_date DESC
    """
    
    result = oracle_forms.extract_and_export(
        extraction_method='database',
        extraction_config={
            'db_config': db_config,
            'query': query
        },
        excel_filename='orders_batch.xlsx',
        excel_config={
            'sheet_name': '订单数据',
            'styled': True,
            'title': '2024年订单列表'
        }
    )
    
    if result['success']:
        print(f"✓ 成功导出 {result['records_extracted']} 条订单记录")
        print(f"  文件位置: {result['excel_file']}")
    else:
        print(f"✗ 导出失败: {result['error']}")
    
    oracle_forms.cleanup()


def example_5_custom_workflow():
    """
    示例 5: 自定义工作流
    
    分步骤执行，更灵活的控制。
    """
    print("\n" + "=" * 60)
    print("示例 5: 自定义工作流")
    print("=" * 60)
    
    oracle_forms = OracleFormsOperations()
    
    # 步骤 1: 从数据库提取数据
    print("\n步骤 1: 连接数据库并查询数据...")
    extract_result = oracle_forms.extract_form_data_from_database(
        db_config={
            'username': 'your_username',
            'password': 'your_password',
            'host': 'localhost',
            'port': 1521,
            'service_name': 'ORCL'
        },
        query="SELECT * FROM employees WHERE rownum <= 10"
    )
    
    if not extract_result['success']:
        print(f"✗ 查询失败: {extract_result['error']}")
        return
    
    print(f"✓ 成功查询 {extract_result['row_count']} 条记录")
    
    # 步骤 2: 数据处理（可选）
    print("\n步骤 2: 处理数据...")
    data = extract_result['data']
    
    # 这里可以对数据进行处理、过滤、转换等
    # 例如：添加计算字段、格式化日期等
    for record in data:
        # 示例：添加一个计算字段
        if 'salary' in record:
            record['annual_salary'] = record['salary'] * 12
    
    print(f"✓ 数据处理完成")
    
    # 步骤 3: 导出到 Excel
    print("\n步骤 3: 导出到 Excel...")
    export_result = oracle_forms.export_to_excel(
        data=data,
        filename='employees_custom.xlsx',
        sheet_name='员工信息',
        styled=True,
        title='员工信息表（含年薪）'
    )
    
    if export_result['success']:
        print(f"✓ 成功导出到 {export_result['filename']}")
    else:
        print(f"✗ 导出失败: {export_result['error']}")
    
    oracle_forms.cleanup()


def main():
    """主函数"""
    print("\nOracle Forms 数据提取到 Excel 示例")
    print("=" * 60)
    print("\n请选择示例:")
    print("1. 从数据库提取数据（推荐）")
    print("2. 通过 Tab 键导航提取")
    print("3. 通过 OCR 识别提取")
    print("4. 批量提取多条记录")
    print("5. 自定义工作流")
    print("0. 退出")
    
    choice = input("\n请输入选项 (0-5): ").strip()
    
    if choice == '1':
        example_1_extract_by_database()
    elif choice == '2':
        example_2_extract_by_tab()
    elif choice == '3':
        example_3_extract_by_ocr()
    elif choice == '4':
        example_4_batch_extract()
    elif choice == '5':
        example_5_custom_workflow()
    elif choice == '0':
        print("\n再见！")
    else:
        print("\n无效的选项")


if __name__ == "__main__":
    main()

