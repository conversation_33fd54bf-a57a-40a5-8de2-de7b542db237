"""基本使用示例"""

from src.core.agent import ComputerUseAgent


def main():
    """基本使用示例"""
    # 创建代理
    agent = ComputerUseAgent()
    
    print("=" * 60)
    print("Computer Use Framework - 基本使用示例")
    print("=" * 60)
    
    # 示例 1: 文件操作
    print("\n示例 1: 创建文件")
    result = agent.execute("创建一个名为 hello.txt 的文件，内容是 Hello World")
    print(f"结果: {result}")
    
    # 示例 2: 读取文件
    print("\n示例 2: 读取文件")
    result = agent.execute("读取 hello.txt 的内容")
    print(f"结果: {result}")
    
    # 示例 3: 打开应用
    print("\n示例 3: 打开记事本")
    result = agent.execute("打开记事本")
    print(f"结果: {result}")
    
    # 示例 4: 截图
    print("\n示例 4: 截取屏幕")
    result = agent.execute("截取当前屏幕")
    print(f"结果: {result}")
    
    # 清理
    agent.cleanup()
    
    print("\n" + "=" * 60)
    print("示例执行完成")
    print("=" * 60)


if __name__ == "__main__":
    main()

