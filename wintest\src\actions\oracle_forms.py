"""Oracle Forms 操作模块"""

import time
from typing import Dict, Any, List, Optional, Tuple
import pyautogui

from src.actions.database import DatabaseOperations
from src.actions.excel_ops import ExcelOperations
from src.actions.input_control import InputControl
from src.actions.screen import ScreenOperations
from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class OracleFormsOperations:
    """Oracle Forms 操作类"""
    
    def __init__(self):
        """初始化 Oracle Forms 操作"""
        self.db_ops = DatabaseOperations()
        self.excel_ops = ExcelOperations()
        self.input_control = InputControl()
        self.screen_ops = ScreenOperations()
    
    def extract_form_fields_by_ocr(
        self,
        field_regions: List[Dict[str, Any]],
        lang: str = 'eng'
    ) -> Dict[str, Any]:
        """
        通过 OCR 提取表单字段
        
        Args:
            field_regions: 字段区域列表，每个元素包含 name 和 region (left, top, width, height)
            lang: OCR 语言
            
        Returns:
            执行结果
        """
        try:
            extracted_data = {}
            
            for field_info in field_regions:
                field_name = field_info.get('name')
                region = field_info.get('region')
                
                if not field_name or not region:
                    continue
                
                # OCR 识别
                result = self.screen_ops.ocr_screenshot(region=region, lang=lang)
                
                if result['success']:
                    extracted_data[field_name] = result['text'].strip()
                else:
                    extracted_data[field_name] = ""
                    logger.warning(f"字段 {field_name} OCR 识别失败")
            
            logger.info(f"成功提取 {len(extracted_data)} 个字段")
            return {
                "success": True,
                "data": extracted_data,
                "field_count": len(extracted_data)
            }
        except Exception as e:
            logger.error(f"提取表单字段失败: {e}")
            return {"success": False, "error": str(e)}
    
    def extract_form_fields_by_tab(
        self,
        field_names: List[str],
        start_position: Tuple[int, int] = None,
        tab_count: int = None
    ) -> Dict[str, Any]:
        """
        通过 Tab 键导航提取表单字段
        
        Args:
            field_names: 字段名称列表
            start_position: 起始位置（点击以聚焦第一个字段）
            tab_count: Tab 键次数（None 表示使用 field_names 长度）
            
        Returns:
            执行结果
        """
        try:
            extracted_data = {}
            
            # 点击起始位置
            if start_position:
                self.input_control.mouse_click(start_position[0], start_position[1])
                time.sleep(0.3)
            
            # 确定 Tab 次数
            if tab_count is None:
                tab_count = len(field_names)
            
            # 遍历字段
            for i, field_name in enumerate(field_names):
                if i >= tab_count:
                    break
                
                # 选择当前字段内容
                self.input_control.keyboard_hotkey('ctrl', 'a')
                time.sleep(0.1)
                
                # 复制内容
                self.input_control.keyboard_hotkey('ctrl', 'c')
                time.sleep(0.1)
                
                # 获取剪贴板内容
                clipboard_result = self.input_control.clipboard_get()
                if clipboard_result['success']:
                    extracted_data[field_name] = clipboard_result['text']
                else:
                    extracted_data[field_name] = ""
                
                # 移动到下一个字段
                if i < tab_count - 1:
                    self.input_control.keyboard_press('tab')
                    time.sleep(0.2)
            
            logger.info(f"成功提取 {len(extracted_data)} 个字段")
            return {
                "success": True,
                "data": extracted_data,
                "field_count": len(extracted_data)
            }
        except Exception as e:
            logger.error(f"提取表单字段失败: {e}")
            return {"success": False, "error": str(e)}
    
    def extract_form_data_from_database(
        self,
        db_config: Dict[str, Any],
        query: str,
        params: tuple = None
    ) -> Dict[str, Any]:
        """
        从数据库提取表单数据
        
        Args:
            db_config: 数据库配置
                - username: 用户名
                - password: 密码
                - host: 主机
                - port: 端口
                - service_name: 服务名
            query: SQL 查询语句
            params: 查询参数
            
        Returns:
            执行结果
        """
        try:
            # 连接数据库
            connect_result = self.db_ops.connect_oracle(
                username=db_config.get('username'),
                password=db_config.get('password'),
                host=db_config.get('host'),
                port=db_config.get('port', 1521),
                service_name=db_config.get('service_name')
            )
            
            if not connect_result['success']:
                return connect_result
            
            # 执行查询
            query_result = self.db_ops.execute_query(query, params)
            
            # 关闭连接
            self.db_ops.close()
            
            if query_result['success']:
                logger.info(f"成功从数据库提取 {query_result['row_count']} 行数据")
            
            return query_result
        except Exception as e:
            logger.error(f"从数据库提取数据失败: {e}")
            self.db_ops.close()
            return {"success": False, "error": str(e)}
    
    def export_to_excel(
        self,
        data: List[Dict[str, Any]],
        filename: str,
        sheet_name: str = "Oracle Forms Data",
        styled: bool = True,
        title: str = None
    ) -> Dict[str, Any]:
        """
        导出数据到 Excel
        
        Args:
            data: 数据列表
            filename: 文件名
            sheet_name: 工作表名称
            styled: 是否使用样式
            title: 标题
            
        Returns:
            执行结果
        """
        try:
            if styled:
                result = self.excel_ops.write_to_excel_styled(
                    data=data,
                    filename=filename,
                    sheet_name=sheet_name,
                    title=title or "Oracle Forms 数据导出"
                )
            else:
                result = self.excel_ops.write_to_excel(
                    data=data,
                    filename=filename,
                    sheet_name=sheet_name
                )
            
            return result
        except Exception as e:
            logger.error(f"导出到 Excel 失败: {e}")
            return {"success": False, "error": str(e)}
    
    def extract_and_export(
        self,
        extraction_method: str,
        extraction_config: Dict[str, Any],
        excel_filename: str,
        excel_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        提取表单数据并导出到 Excel（一站式操作）
        
        Args:
            extraction_method: 提取方法 ('ocr', 'tab', 'database')
            extraction_config: 提取配置
            excel_filename: Excel 文件名
            excel_config: Excel 配置
            
        Returns:
            执行结果
        """
        try:
            # 提取数据
            if extraction_method == 'ocr':
                extract_result = self.extract_form_fields_by_ocr(
                    field_regions=extraction_config.get('field_regions', []),
                    lang=extraction_config.get('lang', 'eng')
                )
            elif extraction_method == 'tab':
                extract_result = self.extract_form_fields_by_tab(
                    field_names=extraction_config.get('field_names', []),
                    start_position=extraction_config.get('start_position'),
                    tab_count=extraction_config.get('tab_count')
                )
            elif extraction_method == 'database':
                extract_result = self.extract_form_data_from_database(
                    db_config=extraction_config.get('db_config', {}),
                    query=extraction_config.get('query', ''),
                    params=extraction_config.get('params')
                )
            else:
                return {"success": False, "error": f"不支持的提取方法: {extraction_method}"}
            
            if not extract_result['success']:
                return extract_result
            
            # 准备数据
            if extraction_method in ['ocr', 'tab']:
                # 单条记录转换为列表
                data = [extract_result['data']]
            else:
                # 数据库查询结果已经是列表
                data = extract_result['data']
            
            # 导出到 Excel
            excel_config = excel_config or {}
            export_result = self.export_to_excel(
                data=data,
                filename=excel_filename,
                sheet_name=excel_config.get('sheet_name', 'Oracle Forms Data'),
                styled=excel_config.get('styled', True),
                title=excel_config.get('title')
            )
            
            if export_result['success']:
                logger.info(f"成功提取并导出数据到 {excel_filename}")
                return {
                    "success": True,
                    "extraction_method": extraction_method,
                    "records_extracted": len(data),
                    "excel_file": export_result['filename']
                }
            else:
                return export_result
        except Exception as e:
            logger.error(f"提取并导出失败: {e}")
            return {"success": False, "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            self.db_ops.close()
        except:
            pass

