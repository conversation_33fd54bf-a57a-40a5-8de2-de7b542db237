"""文件操作示例"""

from src.core.agent import ComputerUseAgent


def main():
    """文件操作示例"""
    agent = ComputerUseAgent()
    
    print("=" * 60)
    print("文件操作示例")
    print("=" * 60)
    
    # 创建文件
    print("\n1. 创建文件")
    result = agent.execute("创建一个名为 test.txt 的文件")
    print(f"   结果: {'成功' if result.get('success') else '失败'}")
    
    # 写入内容
    print("\n2. 写入内容")
    result = agent.execute("在 test.txt 中写入 'Hello from Computer Use Framework!'")
    print(f"   结果: {'成功' if result.get('success') else '失败'}")
    
    # 读取内容
    print("\n3. 读取内容")
    result = agent.execute("读取 test.txt 的内容")
    if result.get('success'):
        content = result.get('results', [{}])[0].get('content', '')
        print(f"   内容: {content}")
    
    # 复制文件
    print("\n4. 复制文件")
    result = agent.execute("复制 test.txt 到 test_copy.txt")
    print(f"   结果: {'成功' if result.get('success') else '失败'}")
    
    # 搜索文件
    print("\n5. 搜索文件")
    result = agent.execute("在当前目录搜索所有 .txt 文件")
    if result.get('success'):
        files = result.get('results', [{}])[0].get('files', [])
        print(f"   找到 {len(files)} 个文件")
    
    # 清理
    agent.cleanup()
    
    print("\n" + "=" * 60)
    print("示例执行完成")
    print("=" * 60)


if __name__ == "__main__":
    main()

