"""动作规划模块"""

from typing import List, Dict, Any
from pydantic import BaseModel, Field

from src.core.intent import Intent, ActionType
from src.utils.logger import get_logger

logger = get_logger()


class Action(BaseModel):
    """动作模型"""
    action_type: ActionType = Field(description="动作类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="动作参数")
    description: str = Field(default="", description="动作描述")
    order: int = Field(default=0, description="执行顺序")


class ActionPlan(BaseModel):
    """动作计划模型"""
    actions: List[Action] = Field(default_factory=list, description="动作列表")
    description: str = Field(default="", description="计划描述")


class ActionPlanner:
    """动作规划器"""
    
    def __init__(self):
        """初始化动作规划器"""
        pass
    
    def plan(self, intent: Intent) -> ActionPlan:
        """
        根据意图生成动作计划
        
        Args:
            intent: 识别出的意图
            
        Returns:
            动作计划
        """
        logger.info(f"规划动作: {intent.action_type}")
        
        # 简单情况：单个意图对应单个动作
        if intent.action_type != ActionType.UNKNOWN:
            action = Action(
                action_type=intent.action_type,
                parameters=intent.parameters,
                description=intent.description,
                order=0
            )
            
            plan = ActionPlan(
                actions=[action],
                description=intent.description
            )
            
            logger.info(f"生成计划: {len(plan.actions)} 个动作")
            return plan
        
        # 未知意图
        logger.warning("无法为未知意图生成计划")
        return ActionPlan(
            actions=[],
            description="无法生成动作计划"
        )
    
    def plan_multi_step(self, intents: List[Intent]) -> ActionPlan:
        """
        规划多步骤动作
        
        Args:
            intents: 意图列表
            
        Returns:
            动作计划
        """
        actions = []
        
        for i, intent in enumerate(intents):
            if intent.action_type != ActionType.UNKNOWN:
                action = Action(
                    action_type=intent.action_type,
                    parameters=intent.parameters,
                    description=intent.description,
                    order=i
                )
                actions.append(action)
        
        plan = ActionPlan(
            actions=actions,
            description=f"多步骤计划: {len(actions)} 个动作"
        )
        
        logger.info(f"生成多步骤计划: {len(plan.actions)} 个动作")
        return plan
    
    def optimize_plan(self, plan: ActionPlan) -> ActionPlan:
        """
        优化动作计划
        
        Args:
            plan: 原始计划
            
        Returns:
            优化后的计划
        """
        # TODO: 实现计划优化逻辑
        # 例如：合并相似动作、重排序以提高效率等
        return plan

