# Playwright MCP 使用指南（VS Code + MCP 客户端）

这份说明帮助你在本地启动 Playwright MCP server，并在 VS Code 或其他支持 MCP 的客户端中使用它。

前提
- Node.js 18+
- 已安装 Playwright（你已运行过 `npx playwright install`）
- 已安装支持 MCP 的客户端/插件（例如 VS Code 的 Playwright MCP 插件、Cursor、Windsurf、Claude Desktop 等）

快速起步（最简单）
1. 在 PowerShell 中启动 Playwright MCP server（HTTP/SSE 端口示例：8931）：

```powershell
npx @playwright/mcp@latest --port 8931
```

2. 在你的 MCP 客户端（例如 VS Code 的 mcp 配置）中把 Playwright 的 URL 指向 HTTP 端点：

```json
{
  "mcpServers": {
    "playwright": {
      "url": "http://localhost:8931/mcp"
    }
  }
}
```

或者在需要通过 `npx` 启动的客户端里，使用标准 config：

```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }
  }
}
```

常用启动参数说明（摘录）
- --port <port>：启用 HTTP/SSE 传输并监听此端口（示例：8931）。
- --host <host>：绑定地址（默认 localhost，使用 0.0.0.0 可对外暴露）。
- --isolated：每次会话使用隔离的浏览器上下文（不会保存到磁盘）。
- --storage-state <path>：为隔离会话提供初始 storage state（例如 cookies/localStorage）。
- --user-data-dir <path>：指定持久化用户数据目录。
- --caps <caps>：启用额外能力，例如 vision、pdf、tracing。
- --viewport-size <size>：指定视窗大小，例如 "1280x720"。

持久化 profile 与隔离模式
- 默认（持久化 profile）：登录状态、cookie 等会保存在本地 profile 目录。Windows 路径示例：
  `%USERPROFILE%\AppData\Local\ms-playwright\mcp-{channel}-profile`
- 隔离（--isolated）：每次会话完成后会丢弃状态，适合测试和无状态交互。

在 VS Code 中使用（推荐步骤）
1. 打开扩展侧栏，确认已安装 Playwright MCP 插件并阅读扩展 README 寻找配置键名与命令。
2. 在工作区设置（`.vscode/settings.json`）中添加或修改 MCP 配置（下面示例里的键名 `playwrightMcp` 是占位，请以你安装的扩展 README 中提到的键名为准）：

```json
{
  "playwrightMcp.serverUrl": "http://localhost:8931/mcp",
  "playwrightMcp.autoAttach": true,
  "playwrightMcp.openPanelOnConnect": true
}
```

3. 使用命令面板（Ctrl+Shift+P）查找并运行扩展提供的命令（例如：Connect/Disconnect/Open Panel/Start Recording 等）。

通过 HTTP/SSE 远程连接
- 如果你在没有显示器的环境（例如 CI 或 worker）运行服务器，务必使用 `--port` 打开 HTTP 端点（SSE）。
- 启动后，客户端可以连接到 `http://host:port/mcp`。示例（curl 测试，仅示例，MCP 协议通常使用 SSE/WebSocket 和特定消息格式）：

```powershell
# 检查端点是否响应
Invoke-WebRequest -Uri http://localhost:8931/ -UseBasicParsing
```

使用浏览器扩展
- Playwright 提供的浏览器扩展允许将已登录的浏览器标签页连接到 MCP，复用你的登录状态与 cookies。请参阅扩展的 README 进行安装与使用。

示例：以隔离模式启动并保存 trace（常用测试/审计）

```powershell
npx @playwright/mcp@latest --port 8931 --isolated --save-trace --output-dir=./mcp-output
```

调试与常见问题
- 无法连接：确认防火墙/端口，确认服务在正确端口监听（`--port`），若绑定非 localhost 请设置 `--host 0.0.0.0`。
- 浏览器无法加载或报错：尝试添加 `--ignore-https-errors` 或检查 `--proxy-server` 设置。
- 需要使用真实浏览器会话：安装并使用 Playwright MCP Bridge 浏览器扩展并用 `--extension` 参数连接。

进阶：将 Playwright MCP 作为 IDE 内置工具
- 许多 IDE/工具（Cursor、Windsurf、Claude Desktop、Goose、LM Studio 等）都支持在其配置中声明 `mcpServers`，并通过 `command`/`args` 或 `url` 字段启动/连接 Playwright MCP。参考本 README 顶部示例。

样例 PowerShell 启动脚本
- 仓库中包含一个简单的启动脚本 `playwright_mcp_launch.ps1`，用于在端口 8931 上启动 Playwright MCP（可直接运行或按需修改参数）。

更多信息
- 运行 `npx @playwright/mcp@latest --help` 以查看最新参数和能力（例如 vision/pdf）。
- 若需要我把 VS Code 的 launch/tasks 配置也加入仓库，请回复 "创建 launch 配置"。

---
文件由仓库示例自动添加。若需 Node 或 Python 端示例脚本来与 MCP HTTP 端点交互，请告诉我偏好（Node/ Python），我会添加示例客户端代码。
