"""文件操作测试"""

import os
import tempfile
import unittest
from pathlib import Path

from src.actions.file_ops import FileOperations


class TestFileOperations(unittest.TestCase):
    """文件操作测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.file_ops = FileOperations()
        self.test_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.test_dir, "test.txt")
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_create_file(self):
        """测试创建文件"""
        result = self.file_ops.create_file(self.test_file, "Hello World")
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(self.test_file))
    
    def test_read_file(self):
        """测试读取文件"""
        # 先创建文件
        content = "Test Content"
        self.file_ops.create_file(self.test_file, content)
        
        # 读取文件
        result = self.file_ops.read_file(self.test_file)
        self.assertTrue(result['success'])
        self.assertEqual(result['content'], content)
    
    def test_write_file(self):
        """测试写入文件"""
        content = "New Content"
        result = self.file_ops.write_file(self.test_file, content)
        self.assertTrue(result['success'])
        
        # 验证内容
        with open(self.test_file, 'r') as f:
            self.assertEqual(f.read(), content)
    
    def test_delete_file(self):
        """测试删除文件"""
        # 先创建文件
        self.file_ops.create_file(self.test_file)
        
        # 删除文件
        result = self.file_ops.delete_file(self.test_file)
        self.assertTrue(result['success'])
        self.assertFalse(os.path.exists(self.test_file))
    
    def test_copy_file(self):
        """测试复制文件"""
        # 创建源文件
        content = "Copy Test"
        self.file_ops.create_file(self.test_file, content)
        
        # 复制文件
        dst_file = os.path.join(self.test_dir, "test_copy.txt")
        result = self.file_ops.copy_file(self.test_file, dst_file)
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(dst_file))
        
        # 验证内容
        with open(dst_file, 'r') as f:
            self.assertEqual(f.read(), content)
    
    def test_move_file(self):
        """测试移动文件"""
        # 创建源文件
        self.file_ops.create_file(self.test_file, "Move Test")
        
        # 移动文件
        dst_file = os.path.join(self.test_dir, "moved.txt")
        result = self.file_ops.move_file(self.test_file, dst_file)
        self.assertTrue(result['success'])
        self.assertFalse(os.path.exists(self.test_file))
        self.assertTrue(os.path.exists(dst_file))
    
    def test_search_files(self):
        """测试搜索文件"""
        # 创建多个文件
        self.file_ops.create_file(os.path.join(self.test_dir, "test1.txt"))
        self.file_ops.create_file(os.path.join(self.test_dir, "test2.txt"))
        self.file_ops.create_file(os.path.join(self.test_dir, "other.log"))
        
        # 搜索 .txt 文件
        result = self.file_ops.search_files(self.test_dir, "*.txt")
        self.assertTrue(result['success'])
        self.assertEqual(result['count'], 2)


if __name__ == '__main__':
    unittest.main()

