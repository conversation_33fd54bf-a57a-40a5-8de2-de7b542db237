"""意图识别模块"""

import json
from typing import Dict, List, Any, Optional
from enum import Enum
from pydantic import BaseModel, Field

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class ActionType(str, Enum):
    """动作类型枚举"""
    FILE_CREATE = "file_create"
    FILE_READ = "file_read"
    FILE_WRITE = "file_write"
    FILE_DELETE = "file_delete"
    FILE_COPY = "file_copy"
    FILE_MOVE = "file_move"
    FILE_SEARCH = "file_search"
    
    APP_OPEN = "app_open"
    APP_CLOSE = "app_close"
    APP_SWITCH = "app_switch"
    
    MOUSE_MOVE = "mouse_move"
    MOUSE_CLICK = "mouse_click"
    MOUSE_DOUBLE_CLICK = "mouse_double_click"
    MOUSE_RIGHT_CLICK = "mouse_right_click"
    
    KEYBOARD_TYPE = "keyboard_type"
    KEYBOARD_PRESS = "keyboard_press"
    KEYBOARD_HOTKEY = "keyboard_hotkey"
    
    BROWSER_OPEN = "browser_open"
    BROWSER_CLICK = "browser_click"
    BROWSER_FILL = "browser_fill"
    BROWSER_EXTRACT = "browser_extract"
    
    SCREEN_SCREENSHOT = "screen_screenshot"
    SCREEN_FIND = "screen_find"
    
    CLIPBOARD_COPY = "clipboard_copy"
    CLIPBOARD_PASTE = "clipboard_paste"
    
    UNKNOWN = "unknown"


class Intent(BaseModel):
    """意图模型"""
    action_type: ActionType = Field(description="动作类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="动作参数")
    confidence: float = Field(default=1.0, description="置信度")
    description: str = Field(default="", description="意图描述")


class IntentRecognizer:
    """意图识别器"""
    
    def __init__(self):
        """初始化意图识别器"""
        self.llm_provider = config.get('llm.provider', 'openai')
        self.model = config.get('llm.model', 'gpt-4')
        self.api_key = config.get('llm.api_key', '')
        
        if not self.api_key:
            logger.warning("未设置 API 密钥，意图识别可能无法正常工作")
    
    def recognize(self, user_input: str) -> Intent:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入的自然语言
            
        Returns:
            识别出的意图
        """
        logger.info(f"识别意图: {user_input}")
        
        try:
            # 使用 LLM 识别意图
            intent_data = self._call_llm(user_input)
            intent = Intent(**intent_data)
            logger.info(f"识别结果: {intent.action_type} - {intent.description}")
            return intent
        except Exception as e:
            logger.error(f"意图识别失败: {e}")
            return Intent(
                action_type=ActionType.UNKNOWN,
                description=f"无法识别意图: {str(e)}"
            )
    
    def _call_llm(self, user_input: str) -> Dict[str, Any]:
        """
        调用 LLM 进行意图识别
        
        Args:
            user_input: 用户输入
            
        Returns:
            意图数据
        """
        system_prompt = self._get_system_prompt()
        
        if self.llm_provider == 'openai':
            return self._call_openai(system_prompt, user_input)
        elif self.llm_provider == 'anthropic':
            return self._call_anthropic(system_prompt, user_input)
        else:
            raise ValueError(f"不支持的 LLM 提供商: {self.llm_provider}")
    
    def _call_openai(self, system_prompt: str, user_input: str) -> Dict[str, Any]:
        """调用 OpenAI API"""
        try:
            from openai import OpenAI
            
            client = OpenAI(api_key=self.api_key)
            response = client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=config.get('llm.temperature', 0.7),
                max_tokens=config.get('llm.max_tokens', 2000),
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
        except Exception as e:
            logger.error(f"OpenAI API 调用失败: {e}")
            raise
    
    def _call_anthropic(self, system_prompt: str, user_input: str) -> Dict[str, Any]:
        """调用 Anthropic API"""
        try:
            from anthropic import Anthropic
            
            client = Anthropic(api_key=self.api_key)
            response = client.messages.create(
                model=self.model,
                max_tokens=config.get('llm.max_tokens', 2000),
                system=system_prompt,
                messages=[
                    {"role": "user", "content": user_input}
                ]
            )
            
            content = response.content[0].text
            # 尝试提取 JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            return json.loads(content)
        except Exception as e:
            logger.error(f"Anthropic API 调用失败: {e}")
            raise
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个计算机操作意图识别助手。你的任务是将用户的自然语言指令转换为结构化的操作意图。

支持的动作类型：
- 文件操作: file_create, file_read, file_write, file_delete, file_copy, file_move, file_search
- 应用控制: app_open, app_close, app_switch
- 鼠标操作: mouse_move, mouse_click, mouse_double_click, mouse_right_click
- 键盘操作: keyboard_type, keyboard_press, keyboard_hotkey
- 浏览器操作: browser_open, browser_click, browser_fill, browser_extract
- 屏幕操作: screen_screenshot, screen_find
- 剪贴板操作: clipboard_copy, clipboard_paste

请以 JSON 格式返回识别结果，格式如下：
{
    "action_type": "动作类型",
    "parameters": {
        "参数名": "参数值"
    },
    "confidence": 0.95,
    "description": "意图描述"
}

示例：
用户输入: "打开记事本"
返回: {
    "action_type": "app_open",
    "parameters": {"app_name": "notepad"},
    "confidence": 1.0,
    "description": "打开记事本应用"
}

用户输入: "创建一个名为test.txt的文件"
返回: {
    "action_type": "file_create",
    "parameters": {"path": "test.txt", "content": ""},
    "confidence": 1.0,
    "description": "创建文件test.txt"
}

请根据用户输入返回相应的 JSON 结果。"""

