#!/usr/bin/env node
const { spawn } = require('child_process');
const EventSource = require('eventsource');
const args = process.argv.slice(2);

// Simple CLI options
const opts = {
  url: 'http://localhost:8931/mcp',
  spawnServer: args.includes('--spawn')
};

let serverProc = null;

async function spawnServer() {
  console.log('Spawning Playwright MCP server via npx...');
  serverProc = spawn('npx', ['@playwright/mcp@latest', '--port', '8931'], { stdio: 'inherit', shell: true });
  serverProc.on('exit', (code) => {
    console.log(`Playwright MCP server exited with code ${code}`);
  });
}

function connectSSE(url) {
  console.log(`Connecting to MCP SSE endpoint: ${url}`);
  const es = new EventSource(url);

  es.onopen = () => console.log('SSE connection opened');
  es.onerror = (err) => console.error('SSE error', err && err.message ? err.message : err);

  es.onmessage = (evt) => {
    try {
      const data = JSON.parse(evt.data);
      console.log('MCP message:', JSON.stringify(data, null, 2));
    } catch (e) {
      console.log('Raw MCP data:', evt.data);
    }
  };

  return es;
}

(async function main() {
  if (opts.spawnServer) {
    await spawnServer();
    // small delay to let server start
    await new Promise((r) => setTimeout(r, 1500));
  }

  const es = connectSSE(opts.url);

  process.on('SIGINT', () => {
    console.log('SIGINT, closing');
    es.close();
    if (serverProc) serverProc.kill('SIGINT');
    process.exit(0);
  });
})();
