#!/usr/bin/env python3
"""
WinTest 基本使用示例

演示如何使用不同的 LLM 提供商创建 Windows 自动化代理
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def example_openai():
    """OpenAI 示例"""
    print("=== OpenAI 示例 ===")

    try:
        from langchain_openai import ChatOpenAI
        from windows_use.agent import Agent

        # 检查 API 密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 请设置 OPENAI_API_KEY 环境变量")
            return

        # 创建 LLM 和代理
        llm = ChatOpenAI(model="gpt-4", temperature=0.2, api_key=api_key)
        agent = Agent(llm=llm, browser='edge', use_vision=False)

        # 执行示例任务
        print("🤖 正在执行: 打开记事本并输入 Hello from OpenAI")
        response = agent.invoke("打开记事本并输入 Hello from OpenAI")
        print(f"✅ 执行结果: {response}")

    except Exception as e:
        print(f"❌ OpenAI 示例失败: {e}")

def example_gemini():
    """Google Gemini 示例"""
    print("\n=== Google Gemini 示例 ===")

    try:
        from langchain_google_genai.chat_models import ChatGoogleGenerativeAI
        from windows_use.agent import Agent

        # 检查 API 密钥
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ 请设置 GOOGLE_API_KEY 环境变量")
            return

        # 创建 LLM 和代理
        llm = ChatGoogleGenerativeAI(model='gemini-2.5-flash', temperature=0.2)
        agent = Agent(llm=llm, browser='edge', use_vision=False)

        # 执行示例任务
        print("🤖 正在执行: 获取当前屏幕尺寸")
        response = agent.invoke("获取当前屏幕尺寸")
        print(f"✅ 执行结果: {response}")

    except Exception as e:
        print(f"❌ Google Gemini 示例失败: {e}")

def example_openrouter():
    """OpenRouter 示例"""
    print("\n=== OpenRouter 示例 ===")

    try:
        from langchain_openai import ChatOpenAI
        from windows_use.agent import Agent

        # 检查 API 密钥
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ 请设置 OPENROUTER_API_KEY 环境变量")
            return

        # 创建 LLM 和代理
        llm = ChatOpenAI(
            model="openai/gpt-4o",
            temperature=0.2,
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1"
        )
        agent = Agent(llm=llm, browser='edge', use_vision=False)

        # 执行示例任务
        print("🤖 正在执行: 打开计算器")
        response = agent.invoke("打开计算器")
        print(f"✅ 执行结果: {response}")

    except Exception as e:
        print(f"❌ OpenRouter 示例失败: {e}")

def example_opendatasky():
    """OpenDataSky 示例"""
    print("\n=== OpenDataSky 示例 ===")

    try:
        from langchain_openai import ChatOpenAI
        from windows_use.agent import Agent

        # 检查 API 密钥
        api_key = os.getenv("OPENDATASKY_API_KEY")
        base_url = os.getenv("OPENDATASKY_BASE_URL")

        if not api_key:
            print("❌ 请设置 OPENDATASKY_API_KEY 环境变量")
            return

        if not base_url:
            print("❌ 请设置 OPENDATASKY_BASE_URL 环境变量")
            return

        # 创建 LLM 和代理
        llm = ChatOpenAI(
            model="claude-sonnet-4-20250514",
            temperature=0.2,
            api_key=api_key,
            base_url=base_url
        )
        agent = Agent(llm=llm, browser='edge', use_vision=False)

        # 执行示例任务
        print("🤖 正在执行: 创建一个名为 test.txt 的文件")
        response = agent.invoke("创建一个名为 test.txt 的文件")
        print(f"✅ 执行结果: {response}")

    except Exception as e:
        print(f"❌ OpenDataSky 示例失败: {e}")

def main():
    """主函数"""
    print("🚀 WinTest 基本示例")
    print("=" * 50)
    
    # 运行各个示例
    example_opendatasky()  # 优先使用 OpenDataSky
    example_openai()
    example_gemini()
    example_openrouter()
    
    print("\n✅ 示例运行完成")
    print("\n💡 提示:")
    print("- 确保已设置相应的 API 密钥")
    print("- 某些操作可能需要管理员权限")
    print("- 建议在安全环境中测试")

if __name__ == "__main__":
    main()
