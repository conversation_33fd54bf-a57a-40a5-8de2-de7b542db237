# WinTest - Windows 自动化服务

🖥️ 基于 [Windows-Use](https://github.com/CursorTouch/Windows-Use) 框架的 Windows 自动化服务，支持多种 LLM 提供商。

## ✨ 特性

- 🤖 **多 LLM 支持**: OpenAI、Google Gemini、OpenRouter
- 🖱️ **GUI 自动化**: 直接与 Windows GUI 交互
- 🌐 **浏览器控制**: 自动化网页操作
- 📁 **文件管理**: 创建、读取、修改文件
- 🎯 **智能识别**: 无需传统计算机视觉模型
- 🔒 **安全可靠**: 内置安全机制和权限控制

## 🛠️ 安装

### 前置要求

- Python 3.8 或更高版本
- Windows 7/8/10/11
- 至少一个 LLM API 密钥

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <your-repo-url>
   cd wintest
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置 API 密钥**
   ```bash
   # 复制环境变量模板
   copy .env.example .env
   
   # 编辑 .env 文件，填入您的 API 密钥
   notepad .env
   ```

## 🚀 快速开始

### 交互模式

```bash
python main.py
```

### 命令模式

```bash
# 执行单个命令
python main.py -c "打开记事本并输入 Hello World"

# 指定 LLM 提供商
python main.py -p openai -c "截取当前屏幕"
```

### 支持的 LLM 提供商

#### OpenDataSky (推荐)
```bash
# 设置环境变量
set OPENDATASKY_API_KEY=API_13a18d141b79a82948f924a99b4b260217c1933b2dc9514b45a13d0e69fda8bcebba685dd31018a4b48dd6f52626c6b1b76e567c7f8393b61b0ca4a798b1a073
set OPENDATASKY_BASE_URL=http://server.opendatasky.com/v1/api/open-ai/ds
# 使用 Claude Sonnet 4 模型
python main.py -p opendatasky
```

#### OpenAI
```bash
# 设置环境变量
set OPENAI_API_KEY=your_api_key_here
python main.py -p openai
```

#### Google Gemini
```bash
# 设置环境变量
set GOOGLE_API_KEY=your_api_key_here
python main.py -p gemini
```

#### OpenRouter
```bash
# 设置环境变量
set OPENROUTER_API_KEY=your_api_key_here
python main.py -p openrouter
```

## 📖 使用示例

### 基本操作

```
# 文件操作
"创建一个名为 test.txt 的文件"
"在 test.txt 中写入 Hello World"
"读取 test.txt 的内容"

# 应用控制
"打开记事本"
"打开计算器"
"关闭当前窗口"

# 浏览器操作
"打开浏览器访问 google.com"
"在搜索框中输入 Python"
"点击搜索按钮"

# 屏幕操作
"截取当前屏幕"
"获取屏幕尺寸"
```

### 高级功能

```
# 复杂任务
"写一篇关于 AI 的短文并保存到桌面"
"从暗色模式切换到亮色模式"
"打开 Excel 并创建一个简单的表格"
```

## ⚙️ 配置

编辑 `config.yaml` 文件来自定义设置：

```yaml
# LLM 配置
llm:
  default_provider: "auto"  # 自动检测可用的提供商
  
# 代理配置
agent:
  browser: "edge"           # 默认浏览器
  use_vision: false         # 视觉功能
  auto_minimize: true       # 自动最小化

# 安全设置
security:
  confirm_dangerous_ops: true  # 确认危险操作
  blocked_apps:               # 禁止控制的应用
    - "cmd.exe"
    - "powershell.exe"
```

## 🔒 安全注意事项

⚠️ **重要提醒**：

1. WinTest 可以直接控制您的 Windows 系统
2. 请在安全的环境中测试
3. 不要在生产环境中运行未经验证的指令
4. 建议在虚拟机或沙盒环境中使用
5. 定期检查日志文件

## 📁 项目结构

```
wintest/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   ├── actions/           # 动作执行器
│   └── utils/             # 工具函数
├── tests/                 # 测试文件
├── examples/              # 示例代码
├── docs/                  # 文档
├── main.py               # 主程序入口
├── config.yaml           # 配置文件
├── requirements.txt      # 依赖列表
├── .env.example          # 环境变量模板
└── README.md            # 项目说明
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Windows-Use](https://github.com/CursorTouch/Windows-Use) - 核心自动化框架
- [LangChain](https://github.com/langchain-ai/langchain) - LLM 集成框架
- [Rich](https://github.com/Textualize/rich) - 终端美化库

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 Issue

---

**Made with ❤️ by WinTest Team**
