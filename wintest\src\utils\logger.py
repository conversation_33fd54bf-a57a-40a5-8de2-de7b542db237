"""日志模块"""

import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional
from colorama import init, Fore, Style

# 初始化 colorama
init(autoreset=True)


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Style.BRIGHT,
    }
    
    def format(self, record):
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{Style.RESET_ALL}"
        return super().format(record)


class Logger:
    """日志管理类"""
    
    _instance: Optional[logging.Logger] = None
    
    @classmethod
    def get_logger(
        cls,
        name: str = "computer_use",
        level: str = "INFO",
        log_file: Optional[str] = None,
        console: bool = True,
        max_file_size_mb: int = 10,
        backup_count: int = 3
    ) -> logging.Logger:
        """
        获取日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
            log_file: 日志文件路径
            console: 是否输出到控制台
            max_file_size_mb: 日志文件最大大小（MB）
            backup_count: 备份文件数量
            
        Returns:
            日志记录器
        """
        if cls._instance is not None:
            return cls._instance
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # 避免重复添加处理器
        if logger.handlers:
            cls._instance = logger
            return logger
        
        # 日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        
        # 控制台处理器
        if console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.DEBUG)
            console_formatter = ColoredFormatter(log_format, date_format)
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=max_file_size_mb * 1024 * 1024,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(log_format, date_format)
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        cls._instance = logger
        return logger


def get_logger() -> logging.Logger:
    """获取默认日志记录器"""
    from src.utils.config import config
    
    return Logger.get_logger(
        level=config.get('logging.level', 'INFO'),
        log_file=config.get('logging.file'),
        console=config.get('logging.console', True),
        max_file_size_mb=config.get('logging.max_file_size_mb', 10),
        backup_count=config.get('logging.backup_count', 3)
    )

