{"name": "playwright-mcp-node-client-example", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "playwright-mcp-node-client-example", "version": "0.0.0", "license": "MIT", "dependencies": {"eventsource": "^1.1.1"}}, "node_modules/eventsource": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/eventsource/-/eventsource-1.1.2.tgz", "integrity": "sha512-xAH3zWhgO2/3KIniEKYPr8plNSzlGINOUqYj0m0u7AB81iRw8b/3E73W6AuU+6klLbaSFmZnaETQ2lXPfAydrA==", "license": "MIT", "engines": {"node": ">=0.12.0"}}}}