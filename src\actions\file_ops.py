"""文件操作模块"""

import os
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class FileOperations:
    """文件操作类"""
    
    def __init__(self):
        """初始化文件操作"""
        self.max_file_size = config.get('security.max_file_size_mb', 100) * 1024 * 1024
        self.allowed_paths = config.get('security.allowed_paths', [])
    
    def _check_path_allowed(self, path: str) -> bool:
        """检查路径是否允许访问"""
        if not self.allowed_paths:
            return True
        
        path_obj = Path(path).resolve()
        for allowed in self.allowed_paths:
            allowed_obj = Path(allowed).resolve()
            try:
                path_obj.relative_to(allowed_obj)
                return True
            except ValueError:
                continue
        
        return False
    
    def create_file(self, path: str, content: str = "") -> Dict[str, Any]:
        """
        创建文件
        
        Args:
            path: 文件路径
            content: 文件内容
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(path):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            file_path = Path(path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"创建文件: {path}")
            return {"success": True, "path": str(file_path.resolve())}
        except Exception as e:
            logger.error(f"创建文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def read_file(self, path: str) -> Dict[str, Any]:
        """
        读取文件
        
        Args:
            path: 文件路径
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(path):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            file_path = Path(path)
            
            if not file_path.exists():
                return {"success": False, "error": "文件不存在"}
            
            if file_path.stat().st_size > self.max_file_size:
                return {"success": False, "error": "文件过大"}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.info(f"读取文件: {path}")
            return {"success": True, "content": content, "path": str(file_path.resolve())}
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def write_file(self, path: str, content: str) -> Dict[str, Any]:
        """
        写入文件
        
        Args:
            path: 文件路径
            content: 文件内容
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(path):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            file_path = Path(path)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"写入文件: {path}")
            return {"success": True, "path": str(file_path.resolve())}
        except Exception as e:
            logger.error(f"写入文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_file(self, path: str) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            path: 文件路径
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(path):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            file_path = Path(path)
            
            if not file_path.exists():
                return {"success": False, "error": "文件不存在"}
            
            if file_path.is_dir():
                shutil.rmtree(file_path)
            else:
                file_path.unlink()
            
            logger.info(f"删除文件: {path}")
            return {"success": True, "path": str(file_path.resolve())}
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def copy_file(self, src: str, dst: str) -> Dict[str, Any]:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(src) or not self._check_path_allowed(dst):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return {"success": False, "error": "源文件不存在"}
            
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            if src_path.is_dir():
                shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
            else:
                shutil.copy2(src_path, dst_path)
            
            logger.info(f"复制文件: {src} -> {dst}")
            return {"success": True, "src": str(src_path.resolve()), "dst": str(dst_path.resolve())}
        except Exception as e:
            logger.error(f"复制文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def move_file(self, src: str, dst: str) -> Dict[str, Any]:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(src) or not self._check_path_allowed(dst):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return {"success": False, "error": "源文件不存在"}
            
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src_path), str(dst_path))
            
            logger.info(f"移动文件: {src} -> {dst}")
            return {"success": True, "src": str(src_path), "dst": str(dst_path.resolve())}
        except Exception as e:
            logger.error(f"移动文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def search_files(self, directory: str, pattern: str) -> Dict[str, Any]:
        """
        搜索文件
        
        Args:
            directory: 搜索目录
            pattern: 搜索模式（支持通配符）
            
        Returns:
            执行结果
        """
        try:
            if not self._check_path_allowed(directory):
                return {"success": False, "error": "路径不在允许的范围内"}
            
            dir_path = Path(directory)
            
            if not dir_path.exists():
                return {"success": False, "error": "目录不存在"}
            
            files = list(dir_path.rglob(pattern))
            file_paths = [str(f.resolve()) for f in files]
            
            logger.info(f"搜索文件: {directory}/{pattern}, 找到 {len(file_paths)} 个文件")
            return {"success": True, "files": file_paths, "count": len(file_paths)}
        except Exception as e:
            logger.error(f"搜索文件失败: {e}")
            return {"success": False, "error": str(e)}

