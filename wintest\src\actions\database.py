"""数据库操作模块"""

from typing import Dict, Any, List, Optional
import oracledb

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class DatabaseOperations:
    """数据库操作类"""
    
    def __init__(self):
        """初始化数据库操作"""
        self.connection = None
        self.cursor = None
    
    def connect_oracle(
        self,
        username: str,
        password: str,
        host: str,
        port: int = 1521,
        service_name: str = None,
        sid: str = None
    ) -> Dict[str, Any]:
        """
        连接 Oracle 数据库
        
        Args:
            username: 用户名
            password: 密码
            host: 主机地址
            port: 端口号
            service_name: 服务名
            sid: SID
            
        Returns:
            执行结果
        """
        try:
            # 构建连接字符串
            if service_name:
                dsn = oracledb.makedsn(host, port, service_name=service_name)
            elif sid:
                dsn = oracledb.makedsn(host, port, sid=sid)
            else:
                return {"success": False, "error": "必须提供 service_name 或 sid"}
            
            # 连接数据库
            self.connection = oracledb.connect(
                user=username,
                password=password,
                dsn=dsn
            )
            self.cursor = self.connection.cursor()
            
            logger.info(f"成功连接到 Oracle 数据库: {host}:{port}")
            return {
                "success": True,
                "host": host,
                "port": port,
                "username": username
            }
        except Exception as e:
            logger.error(f"连接 Oracle 数据库失败: {e}")
            return {"success": False, "error": str(e)}
    
    def execute_query(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """
        执行查询
        
        Args:
            sql: SQL 查询语句
            params: 查询参数
            
        Returns:
            执行结果
        """
        try:
            if not self.cursor:
                return {"success": False, "error": "未连接到数据库"}
            
            # 执行查询
            if params:
                self.cursor.execute(sql, params)
            else:
                self.cursor.execute(sql)
            
            # 获取列名
            columns = [desc[0] for desc in self.cursor.description]
            
            # 获取数据
            rows = self.cursor.fetchall()
            
            # 转换为字典列表
            results = []
            for row in rows:
                results.append(dict(zip(columns, row)))
            
            logger.info(f"查询成功，返回 {len(results)} 行数据")
            return {
                "success": True,
                "columns": columns,
                "data": results,
                "row_count": len(results)
            }
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            return {"success": False, "error": str(e)}
    
    def execute_update(self, sql: str, params: tuple = None) -> Dict[str, Any]:
        """
        执行更新/插入/删除
        
        Args:
            sql: SQL 语句
            params: 参数
            
        Returns:
            执行结果
        """
        try:
            if not self.cursor:
                return {"success": False, "error": "未连接到数据库"}
            
            # 执行语句
            if params:
                self.cursor.execute(sql, params)
            else:
                self.cursor.execute(sql)
            
            # 提交事务
            self.connection.commit()
            
            affected_rows = self.cursor.rowcount
            
            logger.info(f"执行成功，影响 {affected_rows} 行")
            return {
                "success": True,
                "affected_rows": affected_rows
            }
        except Exception as e:
            logger.error(f"执行更新失败: {e}")
            if self.connection:
                self.connection.rollback()
            return {"success": False, "error": str(e)}
    
    def close(self) -> Dict[str, Any]:
        """
        关闭数据库连接
        
        Returns:
            执行结果
        """
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            
            if self.connection:
                self.connection.close()
                self.connection = None
            
            logger.info("数据库连接已关闭")
            return {"success": True}
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
            return {"success": False, "error": str(e)}
    
    def __del__(self):
        """析构函数，确保连接被关闭"""
        try:
            self.close()
        except:
            pass

