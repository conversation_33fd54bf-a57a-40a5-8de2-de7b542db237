# WinTest Configuration

# LLM 配置
llm:
  # 默认提供商: opendatasky, openai, gemini, openrouter
  default_provider: "opendatasky"  # 优先使用 OpenDataSky

  # OpenDataSky 配置
  opendatasky:
    model: "claude-sonnet-4-20250514"
    temperature: 0.2
    max_tokens: 2000
    base_url: "http://server.opendatasky.com/v1/api/open-ai/ds"

  # OpenAI 配置
  openai:
    model: "gpt-4"
    temperature: 0.2
    max_tokens: 2000

  # Google Gemini 配置
  gemini:
    model: "gemini-2.5-flash"
    temperature: 0.2

  # OpenRouter 配置
  openrouter:
    model: "openai/gpt-4o"
    temperature: 0.2

# Windows-Use 代理配置
agent:
  # 默认浏览器: chrome, firefox, edge
  browser: "edge"
  
  # 是否启用视觉功能
  use_vision: false
  
  # 是否自动最小化窗口
  auto_minimize: true
  
  # 超时设置（秒）
  timeout: 30

# 安全设置
security:
  # 是否确认危险操作
  confirm_dangerous_ops: true
  
  # 允许的文件路径（空表示允许所有）
  allowed_paths: []
  
  # 禁止的应用程序
  blocked_apps:
    - "cmd.exe"
    - "powershell.exe"
    - "regedit.exe"
  
  # 最大文件大小（MB）
  max_file_size_mb: 100

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: "wintest.log"
  console: true
  max_file_size_mb: 10
  backup_count: 3

# 功能开关
features:
  # 文件操作
  file_operations: true
  
  # 应用控制
  app_control: true
  
  # 浏览器自动化
  browser_automation: true
  
  # 屏幕操作
  screen_operations: true
  
  # 输入控制
  input_control: true
