"""配置管理模块"""

import os
import yaml
from pathlib import Path
from typing import Any, Dict
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self._config: Dict[str, Any] = {}
        self.load()
    
    def load(self):
        """加载配置文件"""
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
        else:
            # 使用默认配置
            self._config = self._get_default_config()
        
        # 从环境变量覆盖 API 密钥
        if 'llm' in self._config:
            if not self._config['llm'].get('api_key'):
                provider = self._config['llm'].get('provider', 'openai')
                if provider == 'openai':
                    self._config['llm']['api_key'] = os.getenv('OPENAI_API_KEY', '')
                elif provider == 'anthropic':
                    self._config['llm']['api_key'] = os.getenv('ANTHROPIC_API_KEY', '')
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'llm.model'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k)
                if value is None:
                    return default
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, allow_unicode=True, default_flow_style=False)
    
    @staticmethod
    def _get_default_config() -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'llm': {
                'provider': 'openai',
                'model': 'gpt-4',
                'api_key': '',
                'temperature': 0.7,
                'max_tokens': 2000
            },
            'security': {
                'confirm_dangerous_ops': True,
                'allowed_paths': [],
                'blocked_apps': ['cmd.exe', 'powershell.exe'],
                'max_file_size_mb': 100
            },
            'logging': {
                'level': 'INFO',
                'file': 'computer_use.log',
                'console': True
            },
            'actions': {
                'file_ops': {'enabled': True},
                'app_control': {'enabled': True},
                'input_control': {'enabled': True},
                'browser': {'enabled': True},
                'screen': {'enabled': True}
            },
            'agent': {
                'max_retries': 3,
                'retry_delay': 1,
                'verbose': True
            }
        }


# 全局配置实例
config = Config()

