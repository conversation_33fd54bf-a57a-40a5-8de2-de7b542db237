#!/usr/bin/env python3
"""
WinTest 安装验证测试

验证 Windows-Use 和相关依赖是否正确安装
"""

import sys
import importlib
from rich.console import Console
from rich.table import Table

console = Console()

def test_import(module_name, description=""):
    """测试模块导入"""
    try:
        importlib.import_module(module_name)
        return True, "✅"
    except ImportError as e:
        return False, f"❌ {e}"

def main():
    """主测试函数"""
    console.print("🔍 WinTest 安装验证", style="bold blue")
    console.print("=" * 50)
    
    # 创建测试表格
    table = Table(title="依赖检查结果")
    table.add_column("模块", style="cyan")
    table.add_column("描述", style="magenta")
    table.add_column("状态", style="green")
    table.add_column("详情")
    
    # 测试核心依赖
    tests = [
        ("windows_use", "Windows-Use 核心框架"),
        ("windows_use.agent", "Windows-Use 代理"),
        ("PIL", "Pillow 图像库"),
        ("langchain", "LangChain 框架"),
        ("langchain_openai", "OpenAI LangChain"),
        ("langchain_google_genai", "Google Gemini LangChain"),
        ("rich", "Rich 终端库"),
        ("dotenv", "环境变量库"),
        ("pydantic", "数据验证库"),
        ("pyautogui", "GUI 自动化"),
        ("psutil", "系统进程库"),

    ]
    
    success_count = 0
    total_count = len(tests)
    
    for module_name, description in tests:
        success, status = test_import(module_name)
        if success:
            success_count += 1
            table.add_row(module_name, description, "✅ 成功", "已安装")
        else:
            table.add_row(module_name, description, "❌ 失败", status)
    
    console.print(table)
    console.print()
    
    # 显示总结
    if success_count == total_count:
        console.print("🎉 所有依赖都已正确安装！", style="bold green")
        console.print("✅ WinTest 已准备就绪", style="green")
    else:
        console.print(f"⚠️  {total_count - success_count} 个依赖缺失", style="bold yellow")
        console.print("请运行: pip install -r requirements.txt", style="yellow")
    
    console.print()
    console.print("📋 系统信息:")
    console.print(f"Python 版本: {sys.version}")
    console.print(f"平台: {sys.platform}")
    
    # 测试 Windows-Use 基本功能
    console.print("\n🧪 测试 Windows-Use 基本功能...")
    try:
        from windows_use.agent import Agent
        console.print("✅ Agent 类可用", style="green")
        console.print("   支持的浏览器: edge, chrome, firefox")
    except Exception as e:
        console.print(f"❌ Agent 测试失败: {e}", style="red")
    
    console.print("\n💡 下一步:")
    console.print("1. 复制 .env.example 为 .env")
    console.print("2. 在 .env 中设置您的 API 密钥")
    console.print("3. 运行: python main.py")

if __name__ == "__main__":
    main()
