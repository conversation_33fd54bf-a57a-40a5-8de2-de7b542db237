"""输入控制模块（鼠标和键盘）"""

import time
from typing import Dict, Any, List, Tuple, Optional

import pyautogui
import pyperclip

from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()

# 配置 pyautogui
pyautogui.FAILSAFE = True  # 移动鼠标到屏幕角落可以中止
pyautogui.PAUSE = 0.1  # 每次操作后暂停


class InputControl:
    """输入控制类"""
    
    def __init__(self):
        """初始化输入控制"""
        self.typing_speed = config.get('actions.input_control.typing_speed', 0.05)
        self.mouse_speed = config.get('actions.input_control.mouse_speed', 0.5)
    
    # ========== 鼠标操作 ==========
    
    def mouse_move(self, x: int, y: int, duration: float = None) -> Dict[str, Any]:
        """
        移动鼠标
        
        Args:
            x: X 坐标
            y: Y 坐标
            duration: 移动持续时间（秒）
            
        Returns:
            执行结果
        """
        try:
            if duration is None:
                duration = self.mouse_speed
            
            pyautogui.moveTo(x, y, duration=duration)
            
            logger.info(f"移动鼠标到: ({x}, {y})")
            return {"success": True, "x": x, "y": y}
        except Exception as e:
            logger.error(f"移动鼠标失败: {e}")
            return {"success": False, "error": str(e)}
    
    def mouse_click(self, x: int = None, y: int = None, button: str = 'left') -> Dict[str, Any]:
        """
        鼠标点击
        
        Args:
            x: X 坐标（None 表示当前位置）
            y: Y 坐标
            button: 按钮类型 ('left', 'right', 'middle')
            
        Returns:
            执行结果
        """
        try:
            if x is not None and y is not None:
                pyautogui.click(x, y, button=button)
                logger.info(f"点击: ({x}, {y}), 按钮: {button}")
            else:
                pyautogui.click(button=button)
                pos = pyautogui.position()
                logger.info(f"点击当前位置: ({pos.x}, {pos.y}), 按钮: {button}")
            
            return {"success": True, "button": button}
        except Exception as e:
            logger.error(f"鼠标点击失败: {e}")
            return {"success": False, "error": str(e)}
    
    def mouse_double_click(self, x: int = None, y: int = None) -> Dict[str, Any]:
        """
        鼠标双击
        
        Args:
            x: X 坐标
            y: Y 坐标
            
        Returns:
            执行结果
        """
        try:
            if x is not None and y is not None:
                pyautogui.doubleClick(x, y)
                logger.info(f"双击: ({x}, {y})")
            else:
                pyautogui.doubleClick()
                pos = pyautogui.position()
                logger.info(f"双击当前位置: ({pos.x}, {pos.y})")
            
            return {"success": True}
        except Exception as e:
            logger.error(f"鼠标双击失败: {e}")
            return {"success": False, "error": str(e)}
    
    def mouse_right_click(self, x: int = None, y: int = None) -> Dict[str, Any]:
        """
        鼠标右键点击
        
        Args:
            x: X 坐标
            y: Y 坐标
            
        Returns:
            执行结果
        """
        return self.mouse_click(x, y, button='right')
    
    def mouse_scroll(self, clicks: int) -> Dict[str, Any]:
        """
        鼠标滚动
        
        Args:
            clicks: 滚动次数（正数向上，负数向下）
            
        Returns:
            执行结果
        """
        try:
            pyautogui.scroll(clicks)
            logger.info(f"滚动鼠标: {clicks}")
            return {"success": True, "clicks": clicks}
        except Exception as e:
            logger.error(f"鼠标滚动失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_mouse_position(self) -> Dict[str, Any]:
        """
        获取鼠标位置
        
        Returns:
            执行结果
        """
        try:
            pos = pyautogui.position()
            return {"success": True, "x": pos.x, "y": pos.y}
        except Exception as e:
            logger.error(f"获取鼠标位置失败: {e}")
            return {"success": False, "error": str(e)}
    
    # ========== 键盘操作 ==========
    
    def keyboard_type(self, text: str, interval: float = None) -> Dict[str, Any]:
        """
        键盘输入文本
        
        Args:
            text: 要输入的文本
            interval: 按键间隔（秒）
            
        Returns:
            执行结果
        """
        try:
            if interval is None:
                interval = self.typing_speed
            
            pyautogui.write(text, interval=interval)
            
            logger.info(f"输入文本: {text[:50]}...")
            return {"success": True, "text": text}
        except Exception as e:
            logger.error(f"键盘输入失败: {e}")
            return {"success": False, "error": str(e)}
    
    def keyboard_press(self, key: str, presses: int = 1) -> Dict[str, Any]:
        """
        按键
        
        Args:
            key: 按键名称（如 'enter', 'space', 'a' 等）
            presses: 按键次数
            
        Returns:
            执行结果
        """
        try:
            pyautogui.press(key, presses=presses)
            
            logger.info(f"按键: {key} x{presses}")
            return {"success": True, "key": key, "presses": presses}
        except Exception as e:
            logger.error(f"按键失败: {e}")
            return {"success": False, "error": str(e)}
    
    def keyboard_hotkey(self, *keys) -> Dict[str, Any]:
        """
        组合键
        
        Args:
            *keys: 按键列表（如 'ctrl', 'c'）
            
        Returns:
            执行结果
        """
        try:
            pyautogui.hotkey(*keys)
            
            logger.info(f"组合键: {'+'.join(keys)}")
            return {"success": True, "keys": list(keys)}
        except Exception as e:
            logger.error(f"组合键失败: {e}")
            return {"success": False, "error": str(e)}
    
    # ========== 剪贴板操作 ==========
    
    def clipboard_copy(self, text: str = None) -> Dict[str, Any]:
        """
        复制到剪贴板
        
        Args:
            text: 要复制的文本（None 表示使用 Ctrl+C）
            
        Returns:
            执行结果
        """
        try:
            if text is not None:
                pyperclip.copy(text)
                logger.info(f"复制到剪贴板: {text[:50]}...")
            else:
                pyautogui.hotkey('ctrl', 'c')
                time.sleep(0.1)
                text = pyperclip.paste()
                logger.info(f"使用 Ctrl+C 复制")
            
            return {"success": True, "text": text}
        except Exception as e:
            logger.error(f"复制失败: {e}")
            return {"success": False, "error": str(e)}
    
    def clipboard_paste(self) -> Dict[str, Any]:
        """
        从剪贴板粘贴
        
        Returns:
            执行结果
        """
        try:
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(0.1)
            
            logger.info("粘贴剪贴板内容")
            return {"success": True}
        except Exception as e:
            logger.error(f"粘贴失败: {e}")
            return {"success": False, "error": str(e)}
    
    def clipboard_get(self) -> Dict[str, Any]:
        """
        获取剪贴板内容
        
        Returns:
            执行结果
        """
        try:
            text = pyperclip.paste()
            logger.info(f"获取剪贴板内容: {text[:50]}...")
            return {"success": True, "text": text}
        except Exception as e:
            logger.error(f"获取剪贴板内容失败: {e}")
            return {"success": False, "error": str(e)}

