"""Computer Use Agent - 主代理类"""

import time
from typing import Dict, Any, Optional

from src.core.intent import IntentRecognizer, ActionType, Intent
from src.core.planner import ActionPlanner, ActionPlan, Action
from src.actions.file_ops import FileOperations
from src.actions.app_control import AppControl
from src.actions.input_control import InputControl
from src.actions.browser import BrowserControl
from src.actions.screen import ScreenOperations
from src.utils.logger import get_logger
from src.utils.config import config

logger = get_logger()


class ComputerUseAgent:
    """Computer Use 代理 - 通过自然语言操作计算机"""
    
    def __init__(self):
        """初始化代理"""
        logger.info("初始化 Computer Use Agent")
        
        # 核心组件
        self.intent_recognizer = IntentRecognizer()
        self.action_planner = ActionPlanner()
        
        # 动作执行器
        self.file_ops = FileOperations()
        self.app_control = AppControl()
        self.input_control = InputControl()
        self.browser_control = BrowserControl()
        self.screen_ops = ScreenOperations()
        
        # 配置
        self.max_retries = config.get('agent.max_retries', 3)
        self.retry_delay = config.get('agent.retry_delay', 1)
        self.verbose = config.get('agent.verbose', True)
        self.confirm_before_execute = config.get('agent.confirm_before_execute', False)
        self.confirm_dangerous = config.get('security.confirm_dangerous_ops', True)
        
        logger.info("Agent 初始化完成")
    
    def execute(self, user_input: str) -> Dict[str, Any]:
        """
        执行用户指令
        
        Args:
            user_input: 用户的自然语言指令
            
        Returns:
            执行结果
        """
        logger.info(f"收到用户指令: {user_input}")
        
        try:
            # 1. 识别意图
            intent = self.intent_recognizer.recognize(user_input)
            
            if intent.action_type == ActionType.UNKNOWN:
                return {
                    "success": False,
                    "error": "无法理解指令",
                    "description": intent.description
                }
            
            # 2. 生成计划
            plan = self.action_planner.plan(intent)
            
            if not plan.actions:
                return {
                    "success": False,
                    "error": "无法生成执行计划"
                }
            
            # 3. 确认执行（如果需要）
            if self.confirm_before_execute or (self.confirm_dangerous and self._is_dangerous(intent)):
                if not self._confirm_execution(plan):
                    return {
                        "success": False,
                        "error": "用户取消执行"
                    }
            
            # 4. 执行计划
            result = self._execute_plan(plan)
            
            return result
        except Exception as e:
            logger.error(f"执行指令失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _execute_plan(self, plan: ActionPlan) -> Dict[str, Any]:
        """
        执行动作计划
        
        Args:
            plan: 动作计划
            
        Returns:
            执行结果
        """
        results = []
        
        for action in plan.actions:
            logger.info(f"执行动作: {action.action_type} - {action.description}")
            
            # 执行动作（带重试）
            result = self._execute_action_with_retry(action)
            results.append(result)
            
            # 如果失败且不继续，则停止
            if not result.get("success", False):
                logger.warning(f"动作执行失败: {action.action_type}")
                break
        
        # 汇总结果
        all_success = all(r.get("success", False) for r in results)
        
        return {
            "success": all_success,
            "plan_description": plan.description,
            "actions_count": len(results),
            "results": results
        }
    
    def _execute_action_with_retry(self, action: Action) -> Dict[str, Any]:
        """
        执行动作（带重试）
        
        Args:
            action: 动作
            
        Returns:
            执行结果
        """
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                result = self._execute_action(action)
                
                if result.get("success", False):
                    return result
                
                last_error = result.get("error", "未知错误")
                
                if attempt < self.max_retries - 1:
                    logger.warning(f"动作执行失败，重试 {attempt + 1}/{self.max_retries}")
                    time.sleep(self.retry_delay)
            except Exception as e:
                last_error = str(e)
                logger.error(f"动作执行异常: {e}")
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
        
        return {
            "success": False,
            "error": f"重试 {self.max_retries} 次后仍然失败: {last_error}"
        }
    
    def _execute_action(self, action: Action) -> Dict[str, Any]:
        """
        执行单个动作
        
        Args:
            action: 动作
            
        Returns:
            执行结果
        """
        action_type = action.action_type
        params = action.parameters
        
        # 文件操作
        if action_type == ActionType.FILE_CREATE:
            return self.file_ops.create_file(params.get('path'), params.get('content', ''))
        elif action_type == ActionType.FILE_READ:
            return self.file_ops.read_file(params.get('path'))
        elif action_type == ActionType.FILE_WRITE:
            return self.file_ops.write_file(params.get('path'), params.get('content'))
        elif action_type == ActionType.FILE_DELETE:
            return self.file_ops.delete_file(params.get('path'))
        elif action_type == ActionType.FILE_COPY:
            return self.file_ops.copy_file(params.get('src'), params.get('dst'))
        elif action_type == ActionType.FILE_MOVE:
            return self.file_ops.move_file(params.get('src'), params.get('dst'))
        elif action_type == ActionType.FILE_SEARCH:
            return self.file_ops.search_files(params.get('directory'), params.get('pattern'))
        
        # 应用控制
        elif action_type == ActionType.APP_OPEN:
            return self.app_control.open_app(params.get('app_name'), params.get('args'))
        elif action_type == ActionType.APP_CLOSE:
            return self.app_control.close_app(params.get('app_name'), params.get('pid'))
        elif action_type == ActionType.APP_SWITCH:
            return self.app_control.switch_to_app(params.get('app_name'))
        
        # 鼠标操作
        elif action_type == ActionType.MOUSE_MOVE:
            return self.input_control.mouse_move(params.get('x'), params.get('y'))
        elif action_type == ActionType.MOUSE_CLICK:
            return self.input_control.mouse_click(params.get('x'), params.get('y'))
        elif action_type == ActionType.MOUSE_DOUBLE_CLICK:
            return self.input_control.mouse_double_click(params.get('x'), params.get('y'))
        elif action_type == ActionType.MOUSE_RIGHT_CLICK:
            return self.input_control.mouse_right_click(params.get('x'), params.get('y'))
        
        # 键盘操作
        elif action_type == ActionType.KEYBOARD_TYPE:
            return self.input_control.keyboard_type(params.get('text'))
        elif action_type == ActionType.KEYBOARD_PRESS:
            return self.input_control.keyboard_press(params.get('key'))
        elif action_type == ActionType.KEYBOARD_HOTKEY:
            return self.input_control.keyboard_hotkey(*params.get('keys', []))
        
        # 浏览器操作
        elif action_type == ActionType.BROWSER_OPEN:
            return self.browser_control.open_url(params.get('url'))
        elif action_type == ActionType.BROWSER_CLICK:
            return self.browser_control.click_element(params.get('selector'))
        elif action_type == ActionType.BROWSER_FILL:
            return self.browser_control.fill_input(params.get('selector'), params.get('text'))
        elif action_type == ActionType.BROWSER_EXTRACT:
            return self.browser_control.extract_text(params.get('selector'))
        
        # 屏幕操作
        elif action_type == ActionType.SCREEN_SCREENSHOT:
            return self.screen_ops.screenshot(params.get('filename'))
        elif action_type == ActionType.SCREEN_FIND:
            return self.screen_ops.find_on_screen(params.get('image_path'))
        
        # 剪贴板操作
        elif action_type == ActionType.CLIPBOARD_COPY:
            return self.input_control.clipboard_copy(params.get('text'))
        elif action_type == ActionType.CLIPBOARD_PASTE:
            return self.input_control.clipboard_paste()
        
        else:
            return {
                "success": False,
                "error": f"不支持的动作类型: {action_type}"
            }
    
    def _is_dangerous(self, intent: Intent) -> bool:
        """判断操作是否危险"""
        dangerous_actions = [
            ActionType.FILE_DELETE,
            ActionType.APP_CLOSE,
        ]
        return intent.action_type in dangerous_actions
    
    def _confirm_execution(self, plan: ActionPlan) -> bool:
        """确认执行"""
        print(f"\n即将执行: {plan.description}")
        print(f"包含 {len(plan.actions)} 个动作:")
        for i, action in enumerate(plan.actions, 1):
            print(f"  {i}. {action.action_type}: {action.description}")
        
        response = input("\n确认执行? (y/n): ").strip().lower()
        return response in ['y', 'yes', '是']
    
    def cleanup(self):
        """清理资源"""
        logger.info("清理 Agent 资源")
        try:
            self.browser_control.close()
        except:
            pass

